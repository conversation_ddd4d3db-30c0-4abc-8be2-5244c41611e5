import com.android.build.gradle.api.AndroidBasePlugin
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id 'com.android.application' version "8.7.2"
    id 'com.android.library' version "8.7.2" apply false
    id 'com.android.test' version '8.7.2' apply false
    id 'androidx.baselineprofile' version '1.3.3'
    id 'org.jetbrains.kotlin.android' version "2.0.21"
    id 'org.jetbrains.kotlin.plugin.compose' version "2.0.21"
    id 'org.jetbrains.kotlin.plugin.parcelize' version "2.0.21"
    id 'org.jetbrains.kotlin.plugin.serialization' version "2.0.21"
    id "com.google.devtools.ksp" version "2.0.21-1.0.26"
    id 'com.google.protobuf' version "0.9.4"
    id 'dev.rikka.tools.refine' version "4.4.0"
    id 'org.gradle.android.cache-fix' version '3.0.1'
    id 'com.diffplug.spotless' version '6.25.0'
    id 'com.google.gms.google-services' version '4.4.3' apply false
    id 'com.google.firebase.crashlytics' version "3.0.4" apply false
    id 'com.google.firebase.appdistribution' version '5.1.1'
}

allprojects {
    plugins.withType(AndroidBasePlugin).configureEach {
        apply plugin: 'org.gradle.android.cache-fix'

        android {
            compileSdk 35
            defaultConfig {
                minSdk 26
                targetSdk 35
                vectorDrawables.useSupportLibrary = true
            }
            lint {
                abortOnError true
                checkReleaseBuilds false
            }
        }
        dependencies {
            implementation 'androidx.core:core-ktx:1.15.0'
        }
    }

    plugins.withId('com.google.protobuf') {
        def protocVersion = '4.26.1'
        protobuf {
            // Configure the protoc executable
            protoc {
                artifact = "com.google.protobuf:protoc:${protocVersion}"
            }
            generateProtoTasks {
                all().configureEach { task ->
                    task.builtins {
                        remove java
                        java {
                            option "lite"
                        }
                    }
                }
            }
        }
        dependencies {
            implementation "com.google.protobuf:protobuf-javalite:$protocVersion"
        }
    }

    plugins.withType(JavaBasePlugin).configureEach {
        java {
            toolchain.languageVersion = JavaLanguageVersion.of(17)
        }
    }

    tasks.withType(KotlinCompile).configureEach {
        compilerOptions.freeCompilerArgs.add(
            "-Xjvm-default=all",
        )
    }

    ext {
        FRAMEWORK_PREBUILTS_DIR = "$rootDir/prebuilts/libs"
        daggerVersion = '2.52'

        addFrameworkJar = { String name ->
            def frameworkJar = new File(FRAMEWORK_PREBUILTS_DIR, name)
            if (!frameworkJar.exists()) {
                throw new IllegalArgumentException("Framework jar path ${frameworkJar.path} doesn't exist")
            }
            gradle.projectsEvaluated {
                tasks.withType(JavaCompile).configureEach {
                    classpath = files(frameworkJar, classpath)
                }
                tasks.withType(KotlinCompile).configureEach {
                    libraries.from(files(frameworkJar))
                }
            }
            dependencies {
                compileOnly files(frameworkJar)
            }
        }

        compileOnlyCommonJars = {
            dependencies {
                compileOnly fileTree(dir: FRAMEWORK_PREBUILTS_DIR, include: 'SystemUI-core.jar')
                compileOnly fileTree(dir: FRAMEWORK_PREBUILTS_DIR, include: 'SystemUI-statsd.jar')
                compileOnly fileTree(dir: FRAMEWORK_PREBUILTS_DIR, include: 'WindowManager-Shell-14.jar')

                compileOnly projects.compatLib
                compileOnly projects.compatLib.compatLibVQ
                compileOnly projects.compatLib.compatLibVR
                compileOnly projects.compatLib.compatLibVS
                compileOnly projects.compatLib.compatLibVT
                compileOnly projects.compatLib.compatLibVU
            }
        }
    }
}

final def buildCommit = providers.exec {
    commandLine('git', 'rev-parse', '--short=7', 'HEAD')
}.standardOutput.asText.get().trim()

final def versionDisplayCode = 8
final def versionDisplayName = "1.0.0"
final def majorVersion = versionDisplayName.split("\\.")[0]

final def quickstepMinSdk = "29"
final def quickstepMaxSdk = "34"

android {
    namespace "com.android.launcher3"
    defaultConfig {
        // Revive Launcher 14.0 Beta 3
        // See CONTRIBUTING.md#versioning-scheme
        versionCode versionDisplayCode
        versionName "${versionDisplayName}"
        buildConfigField "String", "VERSION_DISPLAY_NAME", "\"${versionDisplayName}\""
        buildConfigField "String", "MAJOR_VERSION", "\"${majorVersion}\""
        buildConfigField "String", "COMMIT_HASH", "\"${buildCommit}\""
        buildConfigField "boolean", "ENABLE_AUTO_INSTALLS_LAYOUT", "false"

        manifestPlaceholders.quickstepMinSdk = quickstepMinSdk
        manifestPlaceholders.quickstepMaxSdk = quickstepMaxSdk
        buildConfigField "int", "QUICKSTEP_MIN_SDK", quickstepMinSdk
        buildConfigField "int", "QUICKSTEP_MAX_SDK", quickstepMaxSdk
    }

    applicationVariants.configureEach { variant ->
        variant.outputs.configureEach {
            def channel = variant.productFlavors.last().name
            outputFileName = "AILauncher.${variant.versionName}.$channel.${variant.buildType.name}.apk"
        }
    }

    androidResources {
        generateLocaleConfig true
    }

    buildFeatures {
        aidl true
        buildConfig true
        resValues true
        compose true
        viewBinding true
    }

    packagingOptions.resources.excludes += [
        "**/*.proto",
        "**/*.bin",
        "**/*.java",
        "**/*.properties",
        "**/*.version",
        "**/*.*_module",
        "com/**",
        "google/**",
        "kotlin/**",
        "kotlinx/**",
        "okhttp3/**",
        "META-INF/services/**",
        "META-INF/com/**",
        "META-INF/licenses/**",
        "META-INF/AL2.0",
        "META-INF/LGPL2.1",
    ]

    // Load all proguard configs from AOSP
    def proguardFilesFromAosp = allprojects
        .collect { it.file("proguard.flags") }
        .findAll { it.exists() }
        .toArray()

    def releaseSigning
    try {
        def keystoreProperties = new Properties()
        keystoreProperties.load(rootProject.file("keystore.properties").newInputStream())
        releaseSigning = signingConfigs.create("release") {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile rootProject.file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    } catch (Exception ignored) {
        releaseSigning = signingConfigs.debug
    }

    buildTypes {
        configureEach {
            pseudoLocalesEnabled true
        }

        debug {
            manifestPlaceholders = [
                ADMOB_APP_ID: "ca-app-pub-3940256099942544~3347511713" // TEST APP ID của Google
            ]
            // applicationIdSuffix ".debug"
            resValue("string", "derived_app_name", "Revive Launcher Debug")

            // Debug AdMob IDs (Test IDs)
            buildConfigField("String", "ADMOB_NATIVE_LANGUAGE", "\"ca-app-pub-3940256099942544/2247696110\"")
            buildConfigField("String", "ADMOB_NATIVE_START_PAGE", "\"ca-app-pub-3940256099942544/2247696110\"")
            buildConfigField("String", "ADMOB_NATIVE_SEARCH_PAGE", "\"ca-app-pub-3940256099942544/2247696110\"")
            buildConfigField("String", "ADMOB_NATIVE_HOME_SETTING", "\"ca-app-pub-3940256099942544/2247696110\"")
            buildConfigField("String", "ADMOB_NATIVE_SETTING", "\"ca-app-pub-3940256099942544/2247696110\"")
            buildConfigField("String", "ADMOB_NATIVE_THEME", "\"ca-app-pub-3940256099942544/2247696110\"")
            buildConfigField("String", "ADMOB_INTERSTITIAL_SPLASH", "\"ca-app-pub-3940256099942544/1033173712\"")
            buildConfigField("String", "ADMOB_INTERSTITIAL_THEME", "\"ca-app-pub-3940256099942544/1033173712\"")
            buildConfigField("String", "ADMOB_INTERSTITIAL_START_PAGE", "\"ca-app-pub-3940256099942544/1033173712\"")

            // K dùng
            buildConfigField("String", "ADMOB_INTERSTITIAL_SETTING", "\"ca-app-pub-3940256099942544/1033173712\"")
        }

        release {
            resValue("string", "derived_app_name", "Revive Launcher")
            minifyEnabled true
            shrinkResources true
            proguardFiles proguardFilesFromAosp + "proguard.pro"
            manifestPlaceholders = [
                ADMOB_APP_ID: "ca-app-pub-7043451012014660~5782424610" // App ID thật của bạn
            ]

            // build_check Id dưới này là dành cho ad thật.
            // Release AdMob IDs (Replace these with your actual production IDs)
            buildConfigField("String", "ADMOB_NATIVE_LANGUAGE", "\"ca-app-pub-7043451012014660/8199870248\"")
            buildConfigField("String", "ADMOB_NATIVE_START_PAGE", "\"ca-app-pub-7043451012014660/4595698971\"")
            buildConfigField("String", "ADMOB_NATIVE_SEARCH_PAGE", "\"ca-app-pub-7043451012014660/6840371329\"")
            buildConfigField("String", "ADMOB_NATIVE_HOME_SETTING", "\"ca-app-pub-7043451012014660/8343372295\"")
            buildConfigField("String", "ADMOB_NATIVE_SETTING", "\"ca-app-pub-7043451012014660/2842799451\"")
            buildConfigField("String", "ADMOB_NATIVE_THEME", "\"ca-app-pub-7043451012014660/1529717781\"")
            buildConfigField("String", "ADMOB_INTERSTITIAL_SPLASH", "\"ca-app-pub-7043451012014660/3932805259\"")
            buildConfigField("String", "ADMOB_INTERSTITIAL_THEME", "\"ca-app-pub-7043451012014660/4260625236\"")
            buildConfigField("String", "ADMOB_INTERSTITIAL_START_PAGE", "\"ca-app-pub-7043451012014660/7729096952\"")

            // K dùng
            buildConfigField("String", "ADMOB_INTERSTITIAL_SETTING", "\"ca-app-pub-7043451012014660/5527289653\"")

            firebaseAppDistribution {
                artifactType="APK"
                releaseNotesFile="releasenotes.txt"
                groups="RevotoTesters"
                serviceCredentialsFile = System.getenv("FIREBASE_CREDENTIALS") ?: "firebase-distribute-credentials.json"
            }

            signingConfig releaseSigning
        }
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
    }

    dependenciesInfo {
        includeInApk = false
        includeInBundle = false
    }

    // See: https://developer.android.com/studio/build/build-variants#flavor-dimensions
    flavorDimensions += ["app", "recents", "channel"]

    productFlavors {
        revive {
            dimension "app"
        }

        withQuickstep {
            dimension "recents"
            minSdk 26
        }

        git {
            applicationId 'com.live.wallpapers.widgets.themes'
            dimension "channel"
        }

        play {
            applicationId "com.live.wallpapers.widgets.themes"
            dimension "channel"
            isDefault true
        }

        configureEach {
            resValue("string", "launcher_component", "${applicationId}/com.live.wallpapers.widgets.themes.AILauncher")
        }
    }

    sourceSets {
        main {
            res.srcDirs = ['res']
            java.srcDirs = ['src', 'src_plugins']
            manifest.srcFile 'AndroidManifest-common.xml'
            proto {
                srcDirs = ['protos/', 'quickstep/protos_overrides/']
            }
        }

        revive {
            java.srcDirs = ['src_flags', 'src_shortcuts_overrides', 'app/src', 'tests/shared']
            aidl.srcDirs = ['app/aidl']
            res.srcDirs = ['app/res', 'platform_frameworks_libs_systemui/animationlib/res']
            manifest.srcFile "app/AndroidManifest.xml"
            assets {
                srcDirs 'app/assets'
            }
            proto {
                srcDirs = ['app/protos/']
            }
        }

        reviveWithQuickstepGit {
            manifest.srcFile "quickstep/AndroidManifest-launcher.xml"
        }

        reviveWithQuickstepPlay {
            manifest.srcFile "quickstep/AndroidManifest-launcher.xml"
        }

        withQuickstep {
            res.srcDirs = ['quickstep/res', 'quickstep/recents_ui_overrides/res']
            java.srcDirs = ['quickstep/src', 'quickstep/recents_ui_overrides/src']
            manifest.srcFile "quickstep/AndroidManifest.xml"
        }
    }

    bundle {
        language {
            enableSplit = false
        }
    }
}

androidComponents {
    onVariants(selector().all()) { variant ->

    }
}

composeCompiler {
    stabilityConfigurationFile = layout.projectDirectory.file("compose_compiler_config.conf")
    reportsDestination = layout.buildDirectory.dir("compose_build_reports")
}

addFrameworkJar('framework-14.jar')

dependencies {
    implementation projects.iconloaderlib
    implementation projects.searchuilib
    implementation projects.animationlib
    implementation platform('com.google.firebase:firebase-bom:33.16.0')
    implementation('com.google.firebase:firebase-crashlytics-ktx')
    implementation('com.google.firebase:firebase-perf-ktx')
    implementation 'com.google.firebase:firebase-messaging-ktx:24.1.2'
    implementation("com.google.firebase:firebase-crashlytics-ndk")
    implementation("com.google.firebase:firebase-analytics")
    implementation 'com.google.android.gms:play-services-ads-identifier:18.2.0'

    // Recents lib dependency
    withQuickstepCompileOnly projects.hiddenApi
    withQuickstepImplementation projects.systemUIShared
    withQuickstepImplementation projects.systemUIAnim
    withQuickstepImplementation projects.systemUnFold
    withQuickstepImplementation projects.systemUIViewCapture
    withQuickstepImplementation projects.systemUILog
    withQuickstepCompileOnly projects.systemUIPlugin
    withQuickstepImplementation projects.systemUIPluginCore
    withQuickstepCompileOnly projects.systemUICommon

    // QuickSwitch Compat
    withQuickstepImplementation projects.compatLib
    withQuickstepImplementation projects.compatLib.compatLibVQ
    withQuickstepImplementation projects.compatLib.compatLibVR
    withQuickstepImplementation projects.compatLib.compatLibVS
    withQuickstepImplementation projects.compatLib.compatLibVT
    withQuickstepImplementation projects.compatLib.compatLibVU

    implementation fileTree(dir: FRAMEWORK_PREBUILTS_DIR, include: 'SystemUI-statsd-14.jar')

    implementation fileTree(dir: FRAMEWORK_PREBUILTS_DIR, include: 'WindowManager-Shell-14.jar')
    withQuickstepCompileOnly fileTree(dir: FRAMEWORK_PREBUILTS_DIR, include: 'framework-14.jar')

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.2'

    implementation 'androidx.profileinstaller:profileinstaller:1.4.1'
    baselineProfile projects.baselineProfile

    implementation "androidx.dynamicanimation:dynamicanimation:1.0.0"
    implementation "androidx.recyclerview:recyclerview:1.3.2"
    implementation "androidx.preference:preference-ktx:1.2.1"

    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.7.3'
    implementation 'com.github.ChickenHook:RestrictionBypass:2.2'
    implementation 'dev.rikka.tools.refine:runtime:4.4.0'

    implementation platform("androidx.compose:compose-bom:2024.10.01")
    implementation "androidx.compose.ui:ui"
    implementation "androidx.compose.ui:ui-util"
    debugImplementation "androidx.compose.ui:ui-tooling"
    implementation "androidx.compose.ui:ui-tooling-preview"
    implementation "androidx.compose.ui:ui-text-google-fonts"
    implementation "androidx.compose.foundation:foundation"
    implementation "androidx.compose.material:material-icons-extended"
    implementation "androidx.compose.material:material"
    implementation "androidx.compose.runtime:runtime-livedata"
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.compose.material3:material3-window-size-class'
    implementation "androidx.activity:activity-compose:1.9.3"
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:2.8.7"
    implementation "androidx.navigation:navigation-compose:2.8.3"
    implementation "androidx.constraintlayout:constraintlayout:2.2.0"
    implementation "androidx.palette:palette-ktx:1.0.0"
    implementation "androidx.slice:slice-core:1.1.0-alpha02"
    def accompanistVersion = '0.36.0'
    implementation "com.google.accompanist:accompanist-adaptive:$accompanistVersion"
    implementation "com.google.accompanist:accompanist-drawablepainter:$accompanistVersion"
    implementation "com.google.accompanist:accompanist-permissions:$accompanistVersion"
    implementation "com.google.accompanist:accompanist-flowlayout:$accompanistVersion"
    implementation "com.google.android.material:material:1.12.0"
    implementation "io.github.fornewid:material-motion-compose-core:1.2.1"
    implementation 'dev.kdrag0n:colorkt:1.0.5'
    def coilVersion = "3.2.0"
    implementation("io.coil-kt.coil3:coil-compose:$coilVersion")
    implementation("io.coil-kt.coil3:coil-network-okhttp:$coilVersion")
    implementation 'me.xdrop:fuzzywuzzy:1.4.0'
    def optoVersion = "1.0.18"
    implementation "com.patrykmichalik.opto:domain:$optoVersion"
    implementation "com.patrykmichalik.opto:core:$optoVersion"
    implementation "com.patrykmichalik.opto:compose:$optoVersion"
    implementation "androidx.datastore:datastore-preferences:1.1.1"
    def retrofitVersion = "2.11.0"
    implementation "com.squareup.retrofit2:retrofit:$retrofitVersion"
    implementation "com.squareup.retrofit2:converter-kotlinx-serialization:$retrofitVersion"

    def roomVersion = '2.6.1'
    implementation "androidx.room:room-runtime:$roomVersion"
    implementation "androidx.room:room-ktx:$roomVersion"
    ksp "androidx.room:room-compiler:$roomVersion"

    implementation "com.github.topjohnwu.libsu:service:6.0.0"

    // Persian Date
    implementation 'com.github.samanzamani:PersianDate:1.7.1'

    implementation 'com.airbnb.android:lottie:6.6.0'
    implementation 'com.airbnb.android:lottie-compose:6.6.0'

    // Compose drag and drop library
    implementation 'sh.calvin.reorderable:reorderable:2.4.0'

    // Haze library for glassmorphic effects
    implementation("dev.chrisbanes.haze:haze:1.6.7")

    // Smartspacer
    implementation('com.kieronquinn.smartspacer:sdk-client:1.0.11') {
        exclude group: "com.github.skydoves", module: "balloon"
    }

    implementation("com.github.android:renderscript-intrinsics-replacement-toolkit:b6363490c3")

    implementation 'com.google.code.gson:gson:2.13.1'

    // Search module
    implementation("com.ailauncher.common:search_model:1.0.3")

    def exoPlayerVersion = '1.3.1'
    implementation "androidx.media3:media3-exoplayer:$exoPlayerVersion"
    implementation "androidx.media3:media3-ui:$exoPlayerVersion"

    // Google Play Services Location
    implementation("com.google.android.gms:play-services-location:21.3.0")

    // Thư viện ads
    def adsVersion = "3.0.1"
    implementation("com.hk.base:ads-provider:$adsVersion")
    implementation("com.hk.base:ads-admob:$adsVersion")

    // Thư viện start page cho luồng start page
    implementation("com.hk.base:ads-startpage:$adsVersion")
    implementation("androidx.cardview:cardview:1.0.0")
}

ksp {
    arg("room.schemaLocation", "$projectDir/schemas")
    arg("room.generateKotlin", "true")
    arg("room.incremental", "true")
}

spotless {
    java {
        target("compatLib/**/src/**/*.java")
        googleJavaFormat().aosp()
        removeUnusedImports()
    }
    kotlin {
        target("app/src/**/*.kt")
        ktlint().customRuleSets([
            "io.nlopez.compose.rules:ktlint:0.4.17",
        ]).editorConfigOverride([
            "ktlint_compose_compositionlocal-allowlist" : "disabled",
            "ktlint_compose_lambda-param-event-trailing": "disabled",
            "ktlint_compose_content-slot-reused"        : "disabled",
        ])
    }
}

apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.firebase.appdistribution'

