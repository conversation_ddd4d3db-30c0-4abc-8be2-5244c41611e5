stages:
  - build

variables:
  G<PERSON>DLE_USER_HOME: "$CI_PROJECT_DIR/.gradle" # Cache Gradle locally
  GIT_CLEAN_FLAGS: none
  GIT_CHECKOUT_FLAGS: "-f"

cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .gradle/

# Packages installation before running script
before_script:
  - echo "Decoding Keystore and creating properties file..."
  # 1. Decode the keystore file using certutil on Windows
  #    The variable holds the path to a temp file containing the base64 text.
  - certutil -f -decode "$env:KEYSTORE_FILE_BASE64" my-release-key.jks
  # 2. Create the keystore.properties file on the fly
  - echo "storeFile=my-release-key.jks" > keystore.properties
  - echo "keyAlias=$env:KEY_ALIAS" >> keystore.properties
  - echo "keyPassword=$env:KEY_PASSWORD" >> keystore.properties
  - echo "storePassword=$env:STORE_PASSWORD" >> keystore.properties
  #  - echo "Cleaning workspace with PowerShell..."
  #  - if (Test-Path .\.gradle) { Remove-Item -Recurse -Force .\.gradle }
  #  - if (Test-Path .\build) { Remove-Item -Recurse -Force .\build }
  #  - if (Test-Path .\.g*) { Remove-Item -Recurse -Force .\.g* }
  - Set-ItemProperty -Path .\gradlew -Name IsReadOnly -Value $false # chmod +x ./gradlew

build:
  tags:
    - launcher
  variables:
    # Add this line to fetch all submodules
    GIT_SUBMODULE_STRATEGY: recursive
  rules:
    - if: '$CI_COMMIT_BRANCH == "build"'
  interruptible: true
  stage: build
  script:
    - echo "Starting Android Build"
    - .\gradlew assembleReviveWithQuickstepPlayRelease appDistributionUploadReviveWithQuickstepPlayRelease --no-daemon
  timeout: 60m
