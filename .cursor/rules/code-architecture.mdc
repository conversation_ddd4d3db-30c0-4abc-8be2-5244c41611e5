---
description: 
globs: 
alwaysApply: true
---
# Code Organization and Architecture

## Project Structure

### Main Components

1. **Launcher Core**
   - Base launcher functionality
   - App drawer and home screen
   - Widget handling
   - Icon management

2. **Quickstep Integration**
   - Recent apps integration
   - Gesture navigation
   - System UI integration

3. **Customization**
   - Theme engine
   - Icon packs
   - Layout options
   - Widget customization

### Key Directories

- `src/com/android/launcher3/` - Core launcher code
- `src/com/live/wallpapers/widgets/themes/` - Launcher-specific implementations
- `src/com/android/quickstep/` - Quickstep integration
- `res/` - Resources and layouts
- `protos/` - Protocol buffer definitions

## Architecture Patterns

1. **MVVM Architecture**
   - ViewModels for UI logic
   - LiveData for reactive updates
   - Repository pattern for data management

2. **Component Organization**
   - Activities for main screens
   - Fragments for UI components
   - Services for background tasks
   - BroadcastReceivers for system events

## Important Files

- [AndroidManifest.xml](mdc:AndroidManifest.xml) - App configuration
- [AndroidManifest-common.xml](mdc:AndroidManifest-common.xml) - Common manifest settings
- [compose_compiler_config.conf](mdc:compose_compiler_config.conf) - Compose configuration

## Code Style

- Follow [.editorconfig](mdc:.editorconfig) for code formatting
- Use Kotlin for new code
- Follow Android architecture components guidelines
- Document public APIs and important classes
