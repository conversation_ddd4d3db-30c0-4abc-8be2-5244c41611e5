---
description:
globs:
alwaysApply: true
---
# Build and Development Workflow

## Building the Project

1. Use `./gradlew` for building:
   - Debug build: `./gradlew assembleDebug`
   - Release build: `./gradlew assembleRelease`
   - Clean build: `./gradlew clean`

2. Build variants available:
   - `reviveWithQuickstepPlayRelease`
   - `reviveWithQuickstepNightlyRelease`
   - `reviveWithQuickstepGithubRelease`
   - `reviveWithQuickstepRelease`

## Development Setup

1. Required tools:
   - Android Studio
   - JDK (version specified in [build.gradle](mdc:build.gradle))
   - Android SDK

2. Configuration files:
   - [local.properties](mdc:local.properties) - SDK location
   - [gradle.properties](mdc:gradle.properties) - Gradle settings
   - [proguard.pro](mdc:proguard.pro) - ProGuard rules

## Testing

- Unit tests in [tests/](mdc:tests) directory
- Use `./gradlew test` to run tests
- CI configuration in [.github/workflows/](mdc:.github/workflows)

## Release Process

1. Version management in [build.gradle](mdc:build.gradle)
2. Release builds through GitHub Actions
3. Distribution channels:
   - Google Play Store
   - GitHub Releases
   - IzzyOnDroid
   - Obtainium
