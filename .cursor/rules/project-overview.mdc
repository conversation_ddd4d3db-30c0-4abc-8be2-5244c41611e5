---
description: 
globs: 
alwaysApply: true
---
# Revive Launcher Project Overview

Revive Launcher based on Launcher3, featuring Pixel Launcher features and rich customization options.

## Key Components

- **Main Source Code**: Located in [src/](mdc:src) directory
- **Build Configuration**: 
  - [build.gradle](mdc:build.gradle) - Main build configuration
  - [settings.gradle](mdc:settings.gradle) - Project settings
  - [gradle.properties](mdc:gradle.properties) - Gradle properties

## Important Directories

- `src/` - Main source code
- `res/` - Resources (layouts, drawables, etc.)
- `app/` - Launcher-specific code
- `quickstep/` - Quickstep integration code
- `tests/` - Test files
- `docs/` - Documentation

## Key Features

- Material You Theming
- At a Glance Widget
- QuickSwitch Support
- Global Search
- Customization Options

## Development Guidelines

- See [CONTRIBUTING.md](mdc:CONTRIBUTING.md) for contribution guidelines
- Follow the code style in [.editorconfig](mdc:.editorconfig)
- Check [AndroidManifest.xml](mdc:AndroidManifest.xml) for app configuration
