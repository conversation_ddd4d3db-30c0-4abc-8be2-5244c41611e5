# Hướng dẫn cài đặt 3D Background

Để 3D Background hoạt động, bạn cần thêm SceneView vào project.

## 1. Thêm dependencies vào build.gradle

Mở file `build.gradle` (app level) và thêm các dependencies sau:

```gradle
dependencies {
    // ... other dependencies
    
    // SceneView
    implementation 'io.github.sceneview:sceneview:0.10.2'
}
```

## 2. Đặt các file model và HDR

Bạn cần đặt các file model 3D (.glb) và file HDR environment trong thư mục assets của project:

```
app/src/main/assets/
├── models/
│   ├── MaterialSuite.glb
│   ├── DamagedHelmet.glb
│   └── Duck.glb
└── environments/
    └── studio_small_09_2k.hdr
```

Bạn có thể tải các mẫu file model 3D GLB từ:
- [glTF Sample Models](https://github.com/KhronosGroup/glTF-Sample-Models/tree/master/2.0)
- [Google Scanned Objects](https://github.com/google/scanned-objects/tree/master)

Và tải file HDR environment từ:
- [HDRi Haven](https://hdrihaven.com/)
- [Poly Haven](https://polyhaven.com/hdris)

## 3. Xử lý quyền truy cập

Ensure the app has READ_EXTERNAL_STORAGE permissions if you want to allow loading models from external storage:

```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

## 4. Tối ưu hiệu suất

Các mô hình 3D có thể tốn nhiều tài nguyên. Nếu gặp vấn đề về hiệu suất, hãy:
- Sử dụng các file model được tối ưu hóa (kích thước nhỏ)
- Giảm độ phức tạp của mô hình
- Tắt các hiệu ứng không cần thiết trong SceneView 