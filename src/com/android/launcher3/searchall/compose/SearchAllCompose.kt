package com.android.launcher3.searchall.compose

import android.app.Application
import android.util.Log
import android.widget.FrameLayout
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.android.launcher3.LauncherState
import com.android.launcher3.R
import com.android.launcher3.searchall.viewmodel.SearchViewModel
import com.android.launcher3.searchall.viewmodel.SearchViewModelFactory
import com.live.wallpapers.widgets.themes.launcher
import com.live.wallpapers.widgets.themes.preferences.preferenceManager
import com.live.wallpapers.widgets.themes.ui.theme.AppTheme
import com.live.wallpapers.widgets.themes.util.ProvideLifecycleState
import com.live.wallpapers.widgets.themes.util.lifecycleState
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.rememberHazeState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Interface to control keyboard visibility from Java code
 */
interface SearchAllController {
    fun showKeyboard()
    fun hideKeyboard()
    fun requestFocus()
    fun clearText()
    fun hideSearchView()
}

// Global instance to hold the current keyboard controller
object SearchAllBridge {
    private var searchAllController: SearchAllController? = null

    @JvmStatic
    fun setController(controller: SearchAllController) {
        searchAllController = controller
    }

    @JvmStatic
    fun showKeyboard() {
        searchAllController?.showKeyboard()
    }

    @JvmStatic
    fun hideKeyboard() {
        searchAllController?.hideKeyboard()
    }

    @JvmStatic
    fun requestFocus() {
        searchAllController?.requestFocus()
    }

    @JvmStatic
    fun clearText() {
        searchAllController?.clearText()
    }

    @JvmStatic
    fun hideSearchView() {
        searchAllController?.hideSearchView()
    }
}

/**
 * Interface to control scroll state from Java code
 */
interface ScrollController {
    fun canScrollBackward(): Boolean
    fun getScrollValue(): Int
    fun getMaxScrollValue(): Int
}

// Global instance to hold the current scroll controller
object SearchScrollBridge {
    private var scrollController: ScrollController? = null

    @JvmStatic
    fun setController(controller: ScrollController) {
        scrollController = controller
    }

    @JvmStatic
    fun canScrollBackward(): Boolean {
        return scrollController?.canScrollBackward() ?: false
    }

    @JvmStatic
    fun getScrollValue(): Int {
        return scrollController?.getScrollValue() ?: 0
    }

    @JvmStatic
    fun getMaxScrollValue(): Int {
        return scrollController?.getMaxScrollValue() ?: 0
    }
}

@Composable
fun SearchAllRoot() {
    AppTheme {
        ProvideLifecycleState {
            Surface(
                modifier = Modifier.fillMaxSize(),
                color = MaterialTheme.colorScheme.background.copy(alpha = 0.0f),
            ) {
                val viewModel: SearchViewModel = viewModel(
                    factory = SearchViewModelFactory(LocalContext.current.applicationContext as Application),
                )
                SearchAllScreen(viewModel)
            }
        }
    }
}

private const val TAG = "SearchAllScreen"

private object ItemKeys {
    const val RESULT_APP = "result_app"
    const val RECENT_APP = "recent_app"
    const val NATIVE_ADS = "native_ads"
    const val RECENT_SEARCH = "recent_search"
}

@Composable
fun SearchAllScreen(viewModel: SearchViewModel = viewModel()) {
    val context = LocalContext.current
    val prefs = preferenceManager()

    // Collect state from ViewModel
    val searchQueryString by viewModel.searchQuery.collectAsState()
    val searchResults by viewModel.searchResults.collectAsState()
    val recentApps by viewModel.recentApps.collectAsState()
    val recentSearches by viewModel.recentSearches.collectAsState(initial = emptyList())
    val isLoading by viewModel.isLoading.collectAsState()
    val isExpanded by viewModel.expandedResults.collectAsState()
    val hasValidQuery by viewModel.hasValidSearchQuery.collectAsState(initial = false)
    val hasUsageAccessPermission by viewModel.hasUsageAccessPermission.collectAsState()

    val itemRects = remember { mutableStateMapOf<String, Rect>() }

    // Local TextFieldValue state to maintain cursor position
    var searchQuery by remember { mutableStateOf(TextFieldValue("")) }

    val hazeState = rememberHazeState()

    // Sync TextFieldValue with ViewModel's String state
    LaunchedEffect(searchQueryString) {
        // Only update if the text content differs, preserve cursor position
        if (searchQuery.text != searchQueryString) {
            searchQuery = TextFieldValue(
                text = searchQueryString,
                selection = TextRange(searchQueryString.length), // Set cursor at end
            )
        }
    }

    LaunchedEffect(recentSearches, hasValidQuery) {
        val shouldRemove = recentSearches.isEmpty() || hasValidQuery
        if (shouldRemove && itemRects.containsKey(ItemKeys.RECENT_SEARCH)) {
            itemRects.remove(ItemKeys.RECENT_SEARCH)
        }
    }

    LaunchedEffect(hasValidQuery) {
        val shouldRemove = !hasValidQuery
        if (shouldRemove && itemRects.containsKey(ItemKeys.RESULT_APP)) {
            itemRects.remove(ItemKeys.RESULT_APP)
        }
    }

    val localDensity = LocalDensity.current
    var bottomBarHeight by remember { mutableStateOf(0.dp) }

    // State to track when to clear focus
    var clearFocus by remember { mutableStateOf(false) }

    // Track if the composable is ready
    var isComposableReady by remember { mutableStateOf(false) }

    // Get the keyboard controller and focus requester
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusRequester = remember { FocusRequester() }
    val coroutineScope = rememberCoroutineScope()

    val resumed = lifecycleState().isAtLeast(Lifecycle.State.RESUMED)
    LaunchedEffect(resumed) {
        if (resumed) {
            Log.d(TAG, "ON_RESUME detected in Compose")
            viewModel.onResume()
        }
    }

    // Create and register a keyboard controller for Java code to use
    remember {
        val controller = object : SearchAllController {
            override fun showKeyboard() {
                keyboardController?.show()
            }

            override fun hideKeyboard() {
                keyboardController?.hide()
            }

            override fun requestFocus() {
                fun requestFocusWithRetry(retryCount: Int = 0) {
                    try {
                        if (isComposableReady) {
                            focusRequester.requestFocus()
                            // Maintain cursor position at the end of current text
                            val currentText = searchQuery.text
                            if (currentText.isNotEmpty()) {
                                searchQuery = searchQuery.copy(
                                    selection = TextRange(currentText.length),
                                )
                            }
                        } else {
                            // Maximum 5 retry attempts
                            if (retryCount < 5) {
                                Log.w(
                                    TAG,
                                    "FocusRequester not ready yet, retrying in 500ms (attempt ${retryCount + 1}/5)",
                                )
                                coroutineScope.launch {
                                    delay(500)
                                    requestFocusWithRetry(retryCount + 1)
                                }
                            }
                        }
                    } catch (e: IllegalStateException) {
                        Log.e(TAG, "Failed to request focus: ${e.message}")
                    }
                }
                requestFocusWithRetry()
            }

            override fun clearText() {
                searchQuery = TextFieldValue("")
                viewModel.clearSearchQuery()
            }

            override fun hideSearchView() {
                viewModel.reloadNativeAd()
            }
        }
        SearchAllBridge.setController(controller)
        controller
    }

    // Use a scrollable column for the content
    val scrollState = rememberScrollState()

    // Create and register a scroll controller for Java code to use
    remember {
        val controller = object : ScrollController {
            override fun canScrollBackward(): Boolean {
                return scrollState.canScrollBackward
            }

            override fun getScrollValue(): Int {
                return scrollState.value
            }

            override fun getMaxScrollValue(): Int {
                return scrollState.maxValue
            }
        }
        SearchScrollBridge.setController(controller)
        controller
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .pointerInput(Unit) {
                detectTapGestures { offset ->
                    val isInsideAnyItem = itemRects.values.any { it.contains(offset) }
                    if (!isInsideAnyItem) {
                        keyboardController?.hide()
                        context.launcher.toggleSearchAll()
                    }
                }
            },
    ) {
        // Scrollable content area
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp, vertical = 12.dp)
                .verticalScroll(scrollState)
                .padding(bottom = bottomBarHeight),
        ) {
            // App section with search results - only show if we have a valid query
            if (hasValidQuery) {
                var resultOffset by remember { mutableStateOf(Offset.Zero) }
                var resultSize by remember { mutableStateOf(IntSize.Zero) }
                ResultAppSection(
                    modifier = Modifier
                        .hazeSource(hazeState)
                        .onGloballyPositioned { coordinates ->
                            resultOffset = coordinates.positionInRoot()
                            resultSize = coordinates.size
                            val resultRect = Rect(
                                resultOffset,
                                Size(resultSize.width.toFloat(), resultSize.height.toFloat()),
                            )
                            itemRects[ItemKeys.RESULT_APP] = resultRect
                        },
                    apps = searchResults,
                    isLoading = isLoading,
                    isExpanded = isExpanded,
                    onToggleExpand = viewModel::toggleExpandedResults,
                    onAppClick = viewModel::launchApp,
                )
                Spacer(modifier = Modifier.height(4.dp))
            }

            // Recent App section
            var recentOffset by remember { mutableStateOf(Offset.Zero) }
            var recentSize by remember { mutableStateOf(IntSize.Zero) }
            RecentAppSection(
                modifier = Modifier
                    .hazeSource(hazeState)
                    .onGloballyPositioned { coordinates ->
                        recentOffset = coordinates.positionInRoot()
                        recentSize = coordinates.size
                        val recentRect = Rect(
                            recentOffset,
                            Size(recentSize.width.toFloat(), recentSize.height.toFloat()),
                        )
                        itemRects[ItemKeys.RECENT_APP] = recentRect
                    },
                recentApps = recentApps,
                onAppClick = viewModel::launchApp,
                hasUsageAccessPermission = hasUsageAccessPermission,
                onOpenSettings = viewModel::openUsageAccessSettings,
            )

            var nativeAdOffset by remember { mutableStateOf(Offset.Zero) }
            var nativeAdSize by remember { mutableStateOf(IntSize.Zero) }

            // Native Ad Container
            Box(modifier = Modifier.padding(vertical = 8.dp)) {
                AndroidView(
                    modifier = Modifier
                        .hazeSource(hazeState)
                        .onGloballyPositioned { coordinates ->
                            nativeAdOffset = coordinates.positionInRoot()
                            nativeAdSize = coordinates.size
                            val nativeAdRect = Rect(
                                nativeAdOffset,
                                Size(nativeAdSize.width.toFloat(), nativeAdSize.height.toFloat()),
                            )
                            itemRects[ItemKeys.NATIVE_ADS] = nativeAdRect
                        }
                        .fillMaxWidth(),
                    factory = { context ->
                        FrameLayout(context).apply {
                            layoutParams = FrameLayout.LayoutParams(
                                FrameLayout.LayoutParams.MATCH_PARENT,
                                FrameLayout.LayoutParams.WRAP_CONTENT,
                            )
                            id = R.id.native_ad_container_in_search_all
                        }
                    },
                    update = { frameLayout ->
                        viewModel.bindNativeAdToView(frameLayout)
                    },
                )
            }

            // Recent search section - only show if we have recent searches
            if (recentSearches.isNotEmpty() && !hasValidQuery) {
                var recentSearchOffset by remember { mutableStateOf(Offset.Zero) }
                var recentSearchSize by remember { mutableStateOf(IntSize.Zero) }
                RecentSearchSection(
                    modifier = Modifier
                        .hazeSource(hazeState)
                        .onGloballyPositioned { coordinates ->
                            recentSearchOffset = coordinates.positionInRoot()
                            recentSearchSize = coordinates.size
                            val recentSearchRect = Rect(
                                recentSearchOffset,
                                Size(
                                    recentSearchSize.width.toFloat(),
                                    recentSearchSize.height.toFloat(),
                                ),
                            )
                            itemRects[ItemKeys.RECENT_SEARCH] = recentSearchRect
                        },
                    recentSearches = recentSearches,
                    onRemoveSearch = viewModel::removeFromRecentSearches,
                    onClearAllSearches = viewModel::clearRecentSearches,
                    onChipClick = { term ->
                        // Update local TextFieldValue with cursor at end
                        searchQuery = TextFieldValue(
                            text = term,
                            selection = TextRange(term.length),
                        )
                        viewModel.handleChipClick(term)
                        clearFocus = true
                    },
                )

                // Add extra space at the bottom for better scrolling
                Spacer(modifier = Modifier.height(4.dp))
            }
        }

        // Search text field overlay at the bottom
        SearchTextField(
            query = searchQuery,
            onQueryChange = { newValue ->
                searchQuery = newValue
                if (newValue.text == "/revieshowdebugmode") {
                    val enableDebugMenu = prefs.enableDebugMenu
                    enableDebugMenu.set(!enableDebugMenu.get())
                    keyboardController?.hide()
                    context.launcher.stateManager.goToState(LauncherState.NORMAL)
                } else {
                    viewModel.updateSearchQuery(newValue.text)
                }
            },
            onSearch = viewModel::handleSearchAction,
            clearFocus = clearFocus,
            autoFocus = false,
            focusRequester = focusRequester,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(horizontal = 16.dp)
                .onSizeChanged { size ->
                    bottomBarHeight = with(localDensity) { size.height.toDp() }
                    // Mark composable as ready when SearchTextField has been laid out
                    isComposableReady = true
                },
            hazeState = hazeState,
        )

        // Reset clearFocus after it's been processed
        if (clearFocus) {
            clearFocus = false
        }
    }
}

@Preview
@Composable
fun SearchAllScreenPreview() {
    MaterialTheme {
        SearchAllScreen()
    }
}
