package com.live.wallpapers.widgets.themes.override

import android.app.Activity
import android.graphics.drawable.Drawable
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.android.launcher3.LauncherAppState
import com.android.launcher3.R
import com.android.launcher3.util.ComponentKey
import com.google.accompanist.drawablepainter.rememberDrawablePainter
import com.live.wallpapers.widgets.themes.preferences.preferenceManager
import com.live.wallpapers.widgets.themes.preferences2.asState
import com.live.wallpapers.widgets.themes.preferences2.preferenceManager2
import com.live.wallpapers.widgets.themes.ui.preferences.LauncherSettingActivity
import com.live.wallpapers.widgets.themes.ui.preferences.components.controls.SwitchPreference
import com.live.wallpapers.widgets.themes.ui.preferences.components.layout.ClickableIcon
import com.live.wallpapers.widgets.themes.ui.preferences.components.layout.PreferenceGroup
import com.live.wallpapers.widgets.themes.ui.preferences.navigation.Routes
import com.live.wallpapers.widgets.themes.ui.util.addIfNotNull
import com.live.wallpapers.widgets.themes.util.navigationBarsOrDisplayCutoutPadding
import kotlinx.coroutines.launch

@Composable
fun CustomizeDialog(
    icon: Drawable,
    title: String,
    onTitleChange: (String) -> Unit,
    defaultTitle: String,
    launchSelectIcon: (() -> Unit)?,
    modifier: Modifier = Modifier,
    content: (@Composable () -> Unit)? = null,
) {
    Column(
        modifier = modifier
            .navigationBarsOrDisplayCutoutPadding()
            .fillMaxWidth(),
    ) {
        val iconPainter = rememberDrawablePainter(drawable = icon)
        Box(
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .padding(vertical = 24.dp)
                .clip(MaterialTheme.shapes.small)
                .addIfNotNull(launchSelectIcon) {
                    clickable(onClick = it)
                }
                .padding(all = 8.dp),
        ) {
            Image(
                painter = iconPainter,
                contentDescription = null,
                modifier = Modifier.size(54.dp),
            )
        }
        OutlinedTextField(
            value = title,
            onValueChange = onTitleChange,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            trailingIcon = {
                if (title != defaultTitle) {
                    ClickableIcon(
                        painter = painterResource(id = R.drawable.ic_undo),
                        onClick = { onTitleChange(defaultTitle) },
                    )
                }
            },
            singleLine = true,
            colors = OutlinedTextFieldDefaults.colors(
                unfocusedContainerColor = MaterialTheme.colorScheme.surface,
                focusedTextColor = MaterialTheme.colorScheme.onSurface,
            ),
            shape = MaterialTheme.shapes.large,
            label = { Text(text = stringResource(id = R.string.label)) },
            isError = title.isEmpty(),
        )
        content?.invoke()
    }
}

@Composable
fun CustomizeAppDialog(
    icon: Drawable,
    defaultTitle: String,
    componentKey: ComponentKey,
    modifier: Modifier = Modifier,
    onClose: () -> Unit,
) {
    val prefs = preferenceManager()
    val preferenceManager2 = preferenceManager2()
    val coroutineScope = rememberCoroutineScope()
    val showComponentNames by preferenceManager2.showComponentNames.asState()
    val hiddenApps by preferenceManager2.hiddenApps.asState()
    val context = LocalContext.current
    var title by remember { mutableStateOf("") }
    val stringKey = componentKey.toString()

    // Track initial and current hidden state separately
    val initialIsHidden = remember(hiddenApps) { hiddenApps.contains(stringKey) }
    var isHidden by remember(hiddenApps) { mutableStateOf(hiddenApps.contains(stringKey)) }

    val request =
        rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode != Activity.RESULT_OK) return@rememberLauncherForActivityResult
            onClose()
        }

    Log.d("TEST", "${Routes.SELECT_ICON}/$componentKey")

    val openIconPicker = {
        val destination = "${Routes.SELECT_ICON}/$componentKey/"
        request.launch(LauncherSettingActivity.createIntent(context, destination))
    }

    DisposableEffect(key1 = null) {
        title = prefs.customAppName[componentKey] ?: defaultTitle
        onDispose {
            // Save title changes
            val previousTitle = prefs.customAppName[componentKey]
            val newTitle = if (title != defaultTitle) title else null
            if (newTitle != previousTitle) {
                prefs.customAppName[componentKey] = newTitle
                val model = LauncherAppState.getInstance(context).model
                model.onPackageChanged(componentKey.componentName.packageName, componentKey.user)
            }

            // Save hidden state changes only if changed (deferred save on dialog close)
            if (isHidden != initialIsHidden) {
                Log.d(
                    "CustomizeDialog",
                    "Saving hidden state change: $initialIsHidden -> $isHidden for $stringKey",
                )
                val newSet = hiddenApps.toMutableSet()
                if (isHidden) newSet.add(stringKey) else newSet.remove(stringKey)
                coroutineScope.launch {
                    preferenceManager2.hiddenApps.set(value = newSet)
                }
            } else {
                Log.d("CustomizeDialog", "No hidden state change, skipping save for $stringKey")
            }
        }
    }
    CustomizeDialog(
        icon = icon,
        title = title,
        onTitleChange = { title = it },
        defaultTitle = defaultTitle,
        launchSelectIcon = openIconPicker,
        modifier = modifier,
    ) {
        PreferenceGroup(
            description = componentKey.componentName.flattenToString(),
            showDescription = showComponentNames,
        ) {
            SwitchPreference(
                checked = isHidden,
                label = stringResource(id = R.string.hide_from_drawer),
                onCheckedChange = { newValue ->
                    // Only update local state, save will happen on dialog close
                    Log.d(
                        "CustomizeDialog",
                        "Switch toggled: $isHidden -> $newValue (deferred save)",
                    )
                    isHidden = newValue
                },
            )
        }
    }
}
