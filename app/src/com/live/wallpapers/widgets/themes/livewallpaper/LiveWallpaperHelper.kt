package com.live.wallpapers.widgets.themes.livewallpaper

import android.app.WallpaperManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ActivityNotFoundException
import android.util.Log
import android.widget.Toast
import com.android.launcher3.R
import com.live.wallpapers.widgets.themes.analytics.logger
import com.live.wallpapers.widgets.themes.analytics.model.LogEventNames
import com.live.wallpapers.widgets.themes.analytics.model.LogEventParams
import java.io.File
import java.io.FileOutputStream
import java.net.URL
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext

object LiveWallpaperHelper {
    private const val TAG = "LiveWallpaperHelper"

    /**
     * Extracts filename from URL
     * @param url The URL to extract filename from
     * @return The filename
     */
    private fun getFileNameFromUrl(url: String): String {
        return url.substring(url.lastIndexOf('/') + 1)
    }

    /**
     * Gets the downloaded video file for a given URL if it exists
     * @param context The context to use for accessing the files directory
     * @param url The URL to check for downloaded file
     * @return The File object if exists, null otherwise
     */
    fun getDownloadedVideoFile(context: Context, url: String): File? {
        return try {
            val fileName = getFileNameFromUrl(url)
            val videosDir = File(context.filesDir, "videos")
            val file = File(videosDir, fileName)
            if (file.exists() && file.length() > 0) {
                Log.d(TAG, "Found downloaded video file: ${file.absolutePath}")
                file
            } else {
                Log.d(TAG, "Downloaded video file not found for URL: $url")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking downloaded video file for URL: $url", e)
            null
        }
    }

    /**
     * Downloads the live wallpaper from the given URL and saves it to the app's files directory
     * @param context The context to use for accessing the files directory
     * @param url The URL to download the live wallpaper from
     * @param onProgress Callback to be called with download progress (0-100)
     * @return The File object representing the downloaded live wallpaper
     */
    private suspend fun downloadLiveWallpaper(
        context: Context,
        url: String,
        screenName: String,
        params: Map<String, Any> = emptyMap(),
        onProgress: (Int) -> Unit,
    ): File =
        withContext(Dispatchers.IO) {
            try {
                // Extract filename from URL
                val fileName = getFileNameFromUrl(url)

                // Create a directory for videos if it doesn't exist
                val videosDir = File(context.filesDir, "videos")
                if (!videosDir.exists()) {
                    videosDir.mkdirs()
                }

                // Create the file to save the video to
                val outputFile = File(videosDir, fileName)

                // If file already exists, return it without downloading again
                if (outputFile.exists() && outputFile.length() > 0) {
                    Log.d(
                        TAG,
                        "File already exists at ${outputFile.absolutePath}, skipping download",
                    )
                    onProgress(100)
                    return@withContext outputFile
                }

                logger().logClickEvent(
                    screenName = screenName,
                    eventName = LogEventNames.BTN_DOWNLOAD_LIVE_WALLPAPER,
                    params = params,
                )

                Log.d(TAG, "Downloading live wallpaper from $url")

                val startTime = System.currentTimeMillis()

                // Download the file
                val connection = URL(url).openConnection()
                val contentLength = connection.contentLength.toLong()
                var totalRead: Long = 0

                // Log file size
                if (contentLength > 0) {
                    val fileSizeMB = contentLength / (1024.0 * 1024.0)
                    Log.d(TAG, "Live wallpaper file size: ${String.format("%.2f", fileSizeMB)} MB ($contentLength bytes)")
                } else {
                    Log.d(TAG, "Live wallpaper file size: Unknown (content-length not available)")
                }

                connection.getInputStream().use { input ->
                    FileOutputStream(outputFile).use { output ->
                        val buffer = ByteArray(4 * 1024) // 4K buffer
                        var read: Int

                        while (input.read(buffer).also { read = it } != -1) {
                            output.write(buffer, 0, read)
                            totalRead += read

                            // Calculate and report progress
                            if (contentLength > 0) {
                                val progress = ((totalRead * 100) / contentLength).toInt()
                                onProgress(progress)
                            }
                        }

                        output.flush()
                    }
                }

                val endTime = System.currentTimeMillis()
                val diff = endTime - startTime

                logger().logClickEvent(
                    screenName = screenName,
                    eventName = LogEventNames.BTN_DOWNLOAD_LIVE_WALLPAPER_SUCCESS,
                    params = params + mapOf(
                        LogEventParams.TIME_DOWNLOAD_LIVE_WALLPAPER to diff,
                    ),
                )

                Log.d(
                    TAG,
                    "Live wallpaper downloaded in ${diff}ms and save to ${outputFile.absolutePath}",
                )
                onProgress(100)
                return@withContext outputFile
            } catch (e: Exception) {
                logger().logClickEvent(
                    screenName = screenName,
                    eventName = LogEventNames.BTN_DOWNLOAD_LIVE_WALLPAPER_ERROR,
                    params = params,
                )
                Log.e(TAG, "Error downloading live wallpaper", e)
                throw e
            }
        }

    /**
     * Downloads thumbnail from URL and saves it to the app's files directory
     * @param context The context to use for accessing the files directory
     * @param thumbnailUrl The URL to download the thumbnail from
     * @return The File object representing the downloaded thumbnail, or null if failed
     */
    private suspend fun downloadThumbnail(
        context: Context,
        thumbnailUrl: String,
        screenName: String,
        params: Map<String, Any> = emptyMap(),
    ): File? = withContext(Dispatchers.IO) {
        try {
            // Create a directory for thumbnails if it doesn't exist
            val thumbnailsDir = File(context.filesDir, "thumbnails")
            if (!thumbnailsDir.exists()) {
                thumbnailsDir.mkdirs()
            }

            // Extract filename from URL or generate one
            val fileName = getFileNameFromUrl(thumbnailUrl).let { name ->
                if (name.contains('.')) name else "$name.jpg"
            }
            val outputFile = File(thumbnailsDir, "thumb_$fileName")

            // If file already exists, return it without downloading again
            if (outputFile.exists() && outputFile.length() > 0) {
                Log.d(TAG, "Thumbnail already exists at ${outputFile.absolutePath}")
                return@withContext outputFile
            }

            logger().logClickEvent(
                screenName = screenName,
                eventName = LogEventNames.BTN_DOWNLOAD_THUMBNAIL,
                params = params,
            )

            Log.d(TAG, "Downloading thumbnail from $thumbnailUrl")

            val startTime = System.currentTimeMillis()

            // Download the thumbnail
            val connection = URL(thumbnailUrl).openConnection()
            connection.getInputStream().use { input ->
                FileOutputStream(outputFile).use { output ->
                    input.copyTo(output)
                    output.flush()
                }
            }

            val endTime = System.currentTimeMillis()
            val diff = endTime - startTime

            logger().logClickEvent(
                screenName = screenName,
                eventName = LogEventNames.BTN_DOWNLOAD_THUMBNAIL_SUCCESS,
                params = params + mapOf(
                    LogEventParams.TIME_DOWNLOAD_THUMBNAIL to diff,
                ),
            )

            Log.d(TAG, "Thumbnail downloaded in ${diff}ms and saved to ${outputFile.absolutePath}")
            return@withContext outputFile
        } catch (e: Exception) {
            logger().logClickEvent(
                screenName = screenName,
                eventName = LogEventNames.BTN_DOWNLOAD_THUMBNAIL_ERROR,
                params = params,
            )
            Log.e(TAG, "Error downloading thumbnail from $thumbnailUrl", e)
            return@withContext null
        }
    }

    /**
     * Sets the given video file as the live wallpaper
     * @param context The context to use for setting the live wallpaper
     * @param videoFile The video file to set as the live wallpaper
     */
    suspend fun setLiveWallpaper(
        context: Context,
        videoFile: File,
        thumbnailFile: File?,
        screenName: String,
        onLaunchIntent: (Intent?) -> Unit = {},
    ) =
        withContext(Dispatchers.IO) {
            try {
                val isLiveWallpaperSet = isLiveWallpaperSet(context)
                if (!isLiveWallpaperSet) {
                    VideoWallpaperPrefs.clearVideoData(context)
                }

                val videoPath = VideoWallpaperPrefs.getVideoPath(context)
                if (videoPath == null) {
                    VideoWallpaperPrefs.setVideoPath(context, videoFile.absolutePath, true)
                }
                VideoWallpaperPrefs.setPreviewVideoPath(context, videoFile.absolutePath, true)

                Log.d(TAG, "Video path saved to preferences: ${videoFile.absolutePath}")

                thumbnailFile?.let {
                    val thumbnailPath = VideoWallpaperPrefs.getThumbnailPath(context)
                    if (thumbnailPath == null) {
                        VideoWallpaperPrefs.setThumbnailPath(
                            context,
                            thumbnailFile.absolutePath,
                            true,
                        )
                    }
                    VideoWallpaperPrefs.setPreviewThumbnailPath(
                        context,
                        thumbnailFile.absolutePath,
                        true,
                    )
                    Log.d(TAG, "Thumbnail path saved to preferences: ${thumbnailFile.absolutePath}")
                }

                // Launch the wallpaper chooser
                withContext(Dispatchers.Main) {
                    val intent = VideoWallpaperService.getIntent(context, screenName)
                    onLaunchIntent(intent)
                }

                Log.d(TAG, "Live wallpaper preview launched")
            } catch (e: Exception) {
                Log.e(TAG, "Error setting live wallpaper", e)
                throw e
            }
        }

    /**
     * Downloads the live wallpaper file and thumbnail simultaneously
     * @param context The context to use for downloading
     * @param url The URL to download the live wallpaper from
     * @param thumbnailUrl The URL to download the thumbnail from
     * @param onProgress Callback to be called with download progress (0-100)
     * @return The downloaded video File object
     */
    suspend fun downloadLiveWallpaperFile(
        context: Context,
        url: String,
        thumbnailUrl: String,
        screenName: String,
        params: Map<String, Any> = emptyMap(),
        onProgress: (Int) -> Unit,
    ): Pair<File, File?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting live wallpaper file and thumbnail download")

            val videoDeferred = async(Dispatchers.IO) {
                downloadLiveWallpaper(
                    context = context,
                    url = url,
                    screenName = screenName,
                    params = params,
                    onProgress = onProgress,
                )
            }

            val thumbnailDeferred = async(Dispatchers.IO) {
                downloadThumbnail(
                    context = context,
                    thumbnailUrl = thumbnailUrl,
                    screenName = screenName,
                    params = params,
                )
            }

            val videoFile = videoDeferred.await()
            val thumbnailFile = thumbnailDeferred.await()

            return@withContext Pair(videoFile, thumbnailFile)
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading live wallpaper file", e)
            throw e
        }
    }

    /**
     * Checks if the live wallpaper is currently set
     * @return true if the live wallpaper is set, false otherwise
     */
    fun isLiveWallpaperSet(context: Context): Boolean {
        try {
            // Check if this live wallpaper is currently set
            val wallpaperManager = WallpaperManager.getInstance(context)
            val currentWallpaper = wallpaperManager.wallpaperInfo

            if (currentWallpaper != null) {
                val isThisServiceSet = ComponentName(
                    context,
                    VideoWallpaperService::class.java,
                ).equals(
                    ComponentName(
                        currentWallpaper.packageName,
                        currentWallpaper.serviceName,
                    ),
                )

                Log.d(TAG, "Is this live wallpaper set: $isThisServiceSet")
                return isThisServiceSet
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if live wallpaper is set", e)
        }

        return false
    }
}
