package com.live.wallpapers.widgets.themes.livewallpaper

import android.app.WallpaperManager
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Build
import android.service.wallpaper.WallpaperService
import android.util.Log
import android.view.Surface
import android.view.SurfaceHolder
import androidx.annotation.OptIn
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.VideoSize
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import com.live.wallpapers.widgets.themes.analytics.logger
import com.live.wallpapers.widgets.themes.analytics.model.LogEventNames
import com.live.wallpapers.widgets.themes.analytics.model.LogEventParams
import java.io.File

class VideoWallpaperService : WallpaperService() {
    companion object {
        const val TAG = "VideoWallpaperService"
        private const val ACTION_UPDATE_VIDEO =
            "com.live.wallpapers.widgets.themes.livewallpaper.action.UPDATE_VIDEO"

        /**
         * Checks which wallpaper picker intent is available on the device and returns the appropriate intent.
         * Returns null if no suitable intent can be found.
         */
        fun getIntent(context: Context, screenName: String): Intent? {
            // Try primary intent for specific live wallpaper
            val primaryIntent = Intent(WallpaperManager.ACTION_CHANGE_LIVE_WALLPAPER).apply {
                putExtra(
                    WallpaperManager.EXTRA_LIVE_WALLPAPER_COMPONENT,
                    ComponentName(context, VideoWallpaperService::class.java),
                )
            }

            // Check if the primary intent can be handled
            if (isIntentResolvable(context, primaryIntent)) {
                Log.e(TAG, "Using change live wallpaper intent")
                logger().logClickEvent(
                    screenName = screenName,
                    eventName = LogEventNames.BTN_LIVE_WALLPAPER_INTENT,
                )
                return primaryIntent
            }

            // Fallback to generic live wallpaper chooser
            val fallbackIntent = Intent(WallpaperManager.ACTION_LIVE_WALLPAPER_CHOOSER)

            // Check if the fallback intent can be handled
            if (isIntentResolvable(context, fallbackIntent)) {
                Log.e(TAG, "Using live wallpaper chooser intent")
                logger().logClickEvent(
                    screenName = screenName,
                    eventName = LogEventNames.BTN_LIVE_WALLPAPER_CHOOSER_INTENT,
                )
                return fallbackIntent
            }

            // No suitable intent found
            Log.e(TAG, "No activity found to handle wallpaper intents")

            // Log error with device information
            val deviceParams = mapOf(
                LogEventParams.DEVICE_MODEL to Build.MODEL,
                LogEventParams.DEVICE_MANUFACTURER to Build.MANUFACTURER,
                LogEventParams.DEVICE_BRAND to Build.BRAND,
                LogEventParams.ANDROID_VERSION to Build.VERSION.RELEASE,
            )

            logger().logClickEvent(
                screenName = screenName,
                eventName = LogEventNames.BTN_LIVE_WALLPAPER_CHOOSER_ERROR,
                params = deviceParams,
            )
            return null
        }

        /**
         * Checks if an intent can be resolved to an activity.
         */
        private fun isIntentResolvable(context: Context, intent: Intent): Boolean {
            val packageManager = context.packageManager
            return intent.resolveActivity(packageManager) != null
        }

        fun sendUpdateVideoIntent(context: Context) {
            val intent = Intent(ACTION_UPDATE_VIDEO).apply {
                setPackage(context.packageName)
            }
            context.sendBroadcast(intent)
        }
    }

    override fun onCreateEngine(): Engine {
        return VideoWallpaperEngine()
    }

    inner class VideoWallpaperEngine : Engine(), Player.Listener {

        private var exoPlayer: ExoPlayer? = null
        private var isSurfaceCreated = false
        private var currentVideoPath: String? = null

        private val updateVideoReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action == ACTION_UPDATE_VIDEO) {
                    if (isSurfaceCreated) {
                        updateVideoPath()
                        initializeExoPlayer(surfaceHolder.surface)
                    }
                }
            }
        }

        override fun onCreate(surfaceHolder: SurfaceHolder) {
            super.onCreate(surfaceHolder)
            Log.d(TAG, "onCreate")
            isSurfaceCreated = false

            val filter = IntentFilter(ACTION_UPDATE_VIDEO)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                registerReceiver(updateVideoReceiver, filter, RECEIVER_NOT_EXPORTED)
            } else {
                @Suppress("UnspecifiedRegisterReceiverFlag")
                registerReceiver(updateVideoReceiver, filter)
            }
        }

        override fun onSurfaceCreated(holder: SurfaceHolder) {
            super.onSurfaceCreated(holder)
            Log.d(TAG, "onSurfaceCreated")
            isSurfaceCreated = true
            initializeExoPlayer(holder.surface)
        }

        override fun onSurfaceDestroyed(holder: SurfaceHolder) {
            super.onSurfaceDestroyed(holder)
            Log.d(TAG, "onSurfaceDestroyed")
            isSurfaceCreated = false
            releaseExoPlayer()
        }

        override fun onVisibilityChanged(visible: Boolean) {
            super.onVisibilityChanged(visible)
            Log.d(TAG, "onVisibilityChanged isVisible: $isVisible - isPreview: $isPreview")

            exoPlayer?.let { player ->
                if (visible) {
                    if (!player.isPlaying) {
                        player.play()
                    }
                } else {
                    player.pause()
                }
            }
        }

        private fun getVideoPath(): String? {
            val videoPath = VideoWallpaperPrefs.getVideoPath(applicationContext)
            if (!videoPath.isNullOrEmpty()) {
                Log.d(TAG, "video path: $videoPath")
                return videoPath
            }
            return null
        }

        private fun getPreviewVideoPath(): String? {
            val videoPath = VideoWallpaperPrefs.getPreviewVideoPath(applicationContext)
            if (!videoPath.isNullOrEmpty()) {
                Log.d(TAG, "preview video path: $videoPath")
                return videoPath
            }
            return null
        }

        private fun getPreviewThumbnailPath(): String? {
            val thumbnailPath = VideoWallpaperPrefs.getPreviewThumbnailPath(applicationContext)
            if (!thumbnailPath.isNullOrEmpty()) {
                Log.d(TAG, "preview thumbnail path: $thumbnailPath")
                return thumbnailPath
            }
            return null
        }

        private fun updateVideoPath() {
            getPreviewVideoPath()?.let { videoPath ->
                VideoWallpaperPrefs.setVideoPath(applicationContext, videoPath, true)
            }
            getPreviewThumbnailPath()?.let { thumbnailPath ->
                VideoWallpaperPrefs.setThumbnailPath(applicationContext, thumbnailPath, true)
            }
        }

        @OptIn(UnstableApi::class)
        private fun initializeExoPlayer(surface: Surface) {
            try {
                // Release any existing player
                releaseExoPlayer()

                // Get video path
                val videoPath = if (isPreview) {
                    getPreviewVideoPath()
                } else {
                    getVideoPath()
                }
                currentVideoPath = videoPath

                Log.d(TAG, "Initializing ExoPlayer with video: $videoPath - isPreview: $isPreview")

                if (videoPath != null && File(videoPath).exists()) {
                    // Create ExoPlayer instance
                    exoPlayer = ExoPlayer.Builder(applicationContext)
                        .build()
                        .apply {
                            // Set video surface - ExoPlayer will automatically fit to surface
                            setVideoSurface(surface)

                            // Add listener for events
                            addListener(this@VideoWallpaperEngine)

                            // Set repeat mode to loop
                            repeatMode = Player.REPEAT_MODE_ONE

                            // Mute audio
                            volume = 0f

                            // Set video scaling mode to crop and center
                            // This will automatically crop and center the video to fit the wallpaper surface
                            videoScalingMode = C.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING

                            // Prepare media item
                            val mediaItem = MediaItem.fromUri(Uri.fromFile(File(videoPath)))
                            setMediaItem(mediaItem)

                            // Prepare and start playback
                            prepare()

                            if (isVisible) {
                                play()
                            }
                        }
                } else {
                    Log.w(TAG, "Video file not found: $videoPath")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error initializing ExoPlayer: ${e.message}", e)
                releaseExoPlayer()
            }
        }

        private fun releaseExoPlayer() {
            exoPlayer?.apply {
                removeListener(this@VideoWallpaperEngine)
                stop()
                release()
            }
            exoPlayer = null
        }

        override fun onVideoSizeChanged(videoSize: VideoSize) {
            super.onVideoSizeChanged(videoSize)
            Log.d(TAG, "onVideoSizeChanged: ${videoSize.width}x${videoSize.height}")

            // ExoPlayer with VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING
            // will automatically handle centering and cropping
            val surfaceFrame = surfaceHolder.surfaceFrame
            val surfaceWidth = surfaceFrame.width()
            val surfaceHeight = surfaceFrame.height()

            Log.d(TAG, "Surface: ${surfaceWidth}x${surfaceHeight}")
        }

        override fun onPlaybackStateChanged(playbackState: Int) {
            super.onPlaybackStateChanged(playbackState)
            when (playbackState) {
                Player.STATE_READY -> {
                    Log.d(TAG, "ExoPlayer ready")
                }

                Player.STATE_ENDED -> {
                    Log.d(TAG, "ExoPlayer ended")
//                    // Should not happen with repeat mode, but restart if needed
//                    exoPlayer?.seekTo(0)
//                    exoPlayer?.play()
                }

                Player.STATE_BUFFERING -> {
                    Log.d(TAG, "ExoPlayer buffering")
                }

                Player.STATE_IDLE -> {
                    Log.d(TAG, "ExoPlayer idle")
                }
            }
        }

        override fun onPlayerError(error: PlaybackException) {
            super.onPlayerError(error)
            Log.e(TAG, "ExoPlayer error: ${error.message}", error)

            // Try to recreate the player
            if (isSurfaceCreated) {
                initializeExoPlayer(surfaceHolder.surface)
            }
        }

        override fun onDestroy() {
            super.onDestroy()
            releaseExoPlayer()
            unregisterReceiver(updateVideoReceiver)
        }
    }
}
