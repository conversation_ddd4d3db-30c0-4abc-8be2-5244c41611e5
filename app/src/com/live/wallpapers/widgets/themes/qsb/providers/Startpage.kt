package com.live.wallpapers.widgets.themes.qsb.providers

import com.live.wallpapers.widgets.themes.animateToAllApps
import com.live.wallpapers.widgets.themes.preferences.PreferenceManager
import com.live.wallpapers.widgets.themes.qsb.ThemingMethod
import com.android.launcher3.Launcher
import com.android.launcher3.R

data object Startpage : QsbSearchProvider(
    id = "startpage",
    name = R.string.search_provider_startpage,
    icon = R.drawable.ic_startpage,
    themingMethod = ThemingMethod.TINT,
    packageName = "",
    website = "https://startpage.com/?segment=startpage.ai.launcher",
    type = QsbSearchProviderType.LOCAL,
    sponsored = true,
) {
    override suspend fun launch(launcher: Launcher, forceWebsite: Boolean) {
        val prefs = PreferenceManager.getInstance(launcher)
        val useWebSuggestions = prefs.searchResultStartPageSuggestion.get()

        if (useWebSuggestions) {
            launcher.animateToAllApps()
            launcher.appsView.searchUiManager.editText?.showKeyboard(true)
        } else {
            super.launch(launcher, forceWebsite)
        }
    }
}
