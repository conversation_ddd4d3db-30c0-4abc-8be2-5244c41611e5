package com.live.wallpapers.widgets.themes.activities.themepack.screens

import android.annotation.SuppressLint
import android.app.Activity
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.windowsizeclass.WindowSizeClass
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.BlurredEdgeTreatment
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.PlayerView
import coil3.compose.SubcomposeAsyncImage
import com.android.launcher3.R
import com.live.wallpapers.widgets.themes.activities.themepack.ScreenBackground
import com.live.wallpapers.widgets.themes.analytics.logger
import com.live.wallpapers.widgets.themes.analytics.model.LogEventNames
import com.live.wallpapers.widgets.themes.analytics.model.LogEventParams
import com.live.wallpapers.widgets.themes.analytics.model.ScreenNames
import com.live.wallpapers.widgets.themes.data.remote.models.ThemeDTO
import com.live.wallpapers.widgets.themes.livewallpaper.LiveWallpaperHelper
import com.live.wallpapers.widgets.themes.livewallpaper.VideoWallpaperService
import com.live.wallpapers.widgets.themes.preferences.getAdapter
import com.live.wallpapers.widgets.themes.preferences2.preferenceManager2
import com.live.wallpapers.widgets.themes.ui.bottomsheet.SetLiveWallpaperBottomSheetContent
import com.live.wallpapers.widgets.themes.ui.preferences.LocalNavController
import com.live.wallpapers.widgets.themes.ui.preferences.components.LiveTag
import com.live.wallpapers.widgets.themes.ui.preferences.components.LoadingOverlay
import com.live.wallpapers.widgets.themes.ui.preferences.components.NoInternetConnectionView
import com.live.wallpapers.widgets.themes.ui.theme.isSelectedThemeDark
import com.live.wallpapers.widgets.themes.ui.util.BottomSheetOptions
import com.live.wallpapers.widgets.themes.ui.util.bottomSheetHandler
import com.live.wallpapers.widgets.themes.util.TrackLazyListScroll
import com.live.wallpapers.widgets.themes.util.dropShadow
import com.live.wallpapers.widgets.themes.util.lifecycleState
import com.live.wallpapers.widgets.themes.util.rememberNetworkState
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ThemePreviewScreen(
    themeId: Int,
    viewModel: ThemePreviewViewModel = viewModel(),
    isFromIntro: Boolean = false,
    onNavigateBackToIntro: () -> Unit = {},
) {
    val context = LocalContext.current
    val bottomSheetHandler = bottomSheetHandler
    val coroutineScope = rememberCoroutineScope()

    val prefs = preferenceManager2()
    val homeTextColorAdapter = prefs.homeTextColor.getAdapter()

    val navController = LocalNavController.current

    val state by viewModel.state.collectAsState()
    val theme = state.theme
    val isLiveWallpaper = theme?.isLiveWallpaper == true

    // State for live wallpaper download
    var isDownloadingWallpaper by remember { mutableStateOf(false) }
    var downloadProgress by remember { mutableStateOf(0) }

    val networkState by rememberNetworkState()
    val isConnected by remember(networkState) {
        derivedStateOf {
            networkState.isConnected()
        }
    }
    var previousNetworkState by remember { mutableStateOf(networkState) }

    LaunchedEffect(networkState) {
        if (!previousNetworkState.isConnected() && networkState.isConnected()) {
            val currentState = viewModel.state.value
            val shouldRefresh =
                currentState.error != null || currentState.theme == null

            if (shouldRefresh) {
                viewModel.loadTheme(themeId)
            }
        }
        previousNetworkState = networkState
    }

    // Load theme when the screen is first displayed
    LaunchedEffect(themeId) {
        viewModel.logViewScreen(themeId)
        viewModel.loadTheme(themeId)
    }

    LaunchedEffect(state.isSuccess) {
        if (state.isSuccess) {
            theme?.wallpaper?.textColor?.let { color ->
                homeTextColorAdapter.onChange(color)
            }
            if (isFromIntro) {
                onNavigateBackToIntro()
            } else {
                navController.popBackStack()
            }
        }
    }

    var isLiveWallpaperSet by remember { mutableStateOf(false) }

    val launcher =
        rememberLauncherForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                if (LiveWallpaperHelper.isLiveWallpaperSet(context)) {
                    if (isLiveWallpaperSet) {
                        VideoWallpaperService.sendUpdateVideoIntent(context)
                    }
                    viewModel.applyThemeWithoutWallpaper()
                } else {
                    Toast.makeText(
                        context,
                        R.string.theme_apply_error_without_message,
                        Toast.LENGTH_LONG,
                    ).show()
                    viewModel.setApplying(false)
                }
            } else {
                viewModel.setApplying(false)
            }
        }

    val onApplyTheme: () -> Unit = {
        logger().logClickEvent(
            screenName = ScreenNames.THEME_PREVIEW,
            eventName = if (isLiveWallpaper) LogEventNames.BTN_APPLY_THEME_LIVE else LogEventNames.BTN_APPLY_THEME,
            params = mapOf(
                LogEventParams.THEME_ID to themeId,
                LogEventParams.IS_VIDEO to isLiveWallpaper,
            ),
        )

        if (isLiveWallpaper) {
            logger().logClickEvent(
                screenName = ScreenNames.THEME_PREVIEW,
                eventName = LogEventNames.VIEW_LIVE_WARNING_POPUP,
                params = mapOf(
                    LogEventParams.THEME_ID to themeId,
                    LogEventParams.IS_VIDEO to true,
                ),
            )
            bottomSheetHandler.show(
                BottomSheetOptions(
                    showDrag = false,
                    sheetModifier = Modifier
                        .padding(
                            start = 16.dp,
                            end = 16.dp,
                            bottom = 8.dp,
                        )
                        .navigationBarsPadding(),
                ) {
                    SetLiveWallpaperBottomSheetContent(
                        onClose = {
                            logger().logClickEvent(
                                screenName = ScreenNames.THEME_PREVIEW,
                                eventName = LogEventNames.BTN_LIVE_WARNING_POPUP_CANCEL,
                                params = mapOf(
                                    LogEventParams.THEME_ID to themeId,
                                    LogEventParams.IS_VIDEO to true,
                                ),
                            )
                            bottomSheetHandler.hide()
                        },
                        onConfirm = {
                            logger().logClickEvent(
                                screenName = ScreenNames.THEME_PREVIEW,
                                eventName = LogEventNames.BTN_LIVE_WARNING_POPUP_OK,
                                params = mapOf(
                                    LogEventParams.THEME_ID to themeId,
                                    LogEventParams.IS_VIDEO to true,
                                ),
                            )
                            bottomSheetHandler.hide()

                            isDownloadingWallpaper = true
                            downloadProgress = 0

                            viewModel.downloadLiveWallpaperTheme(
                                onProgress = { progress ->
                                    downloadProgress = progress
                                },
                                onComplete = { success, videoFile, thumbnailFile ->
                                    isDownloadingWallpaper =
                                        false

                                    if (success && videoFile != null) {
                                        coroutineScope.launch {
                                            LiveWallpaperHelper.setLiveWallpaper(
                                                context = context,
                                                videoFile = videoFile,
                                                thumbnailFile = thumbnailFile,
                                                screenName = ScreenNames.THEME_PREVIEW,
                                                onLaunchIntent = { intent ->
                                                    isLiveWallpaperSet =
                                                        LiveWallpaperHelper.isLiveWallpaperSet(
                                                            context,
                                                        )

                                                    if (intent != null) {
                                                        launcher.launch(intent)
                                                    } else {
                                                        Toast.makeText(
                                                            context,
                                                            R.string.error_wallpaper_chooser,
                                                            Toast.LENGTH_LONG,
                                                        ).show()
                                                        viewModel.setApplying(false)
                                                    }
                                                },
                                            )
                                        }
                                    }
                                },
                            )
                        },
                    )
                },
            )
        } else {
            viewModel.applyTheme()
        }
    }

    Box(
        modifier = Modifier.fillMaxSize(),
    ) {
        ScreenBackground()

        Scaffold(
            contentWindowInsets = WindowInsets(0, 0, 0, 0),
            containerColor = Color.Transparent,
            topBar = {
                TopAppBar(
                    title = {
                        if (isConnected) {
                            theme?.let { theme ->
                                Text(
                                    text = theme.name,
                                    style = MaterialTheme.typography.headlineLarge,
                                )
                            }
                        }
                    },
                    navigationIcon = {
                        IconButton(
                            onClick = {
                                logger().logClickEvent(
                                    screenName = ScreenNames.THEME_PREVIEW,
                                    eventName = LogEventNames.BTN_BACK,
                                )
                                navController.popBackStack()
                            },
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_arrow_back),
                                contentDescription = stringResource(R.string.action_back),
                                modifier = Modifier.size(24.dp),
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color.Transparent,
                    ),
                )
            },
            bottomBar = {
                if (isConnected && theme != null) {
                    BottomBarView(
                        theme = theme,
                        isApplying = state.isApplying,
                        isDownloadingWallpaper = isDownloadingWallpaper,
                        onClick = onApplyTheme,
                    )
                }
            },
        ) { paddingValues ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center,
            ) {
                if (!isConnected || state.error != null) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        Box(
                            modifier = Modifier.weight(1f),
                            contentAlignment = Alignment.Center,
                        ) {
                            NoInternetConnectionView()
                        }

                        Box(
                            modifier = Modifier
                                .padding(horizontal = 16.dp, vertical = 8.dp)
                                .navigationBarsPadding(),
                        ) {
                            Box(
                                modifier = Modifier
                                    .height(48.dp)
                                    .fillMaxWidth()
                                    .clip(RoundedCornerShape(40.dp))
                                    .background(MaterialTheme.colorScheme.primary)
                                    .clickable(
                                        onClick = {
                                            if (!isConnected) {
                                                Toast.makeText(
                                                    context,
                                                    R.string.no_internet_connection_please_check_your_network,
                                                    Toast.LENGTH_SHORT,
                                                ).show()
                                            } else {
                                                viewModel.loadTheme(themeId)
                                            }
                                        },
                                    )
                                    .padding(vertical = 12.dp, horizontal = 24.dp),
                                contentAlignment = Alignment.Center,
                            ) {
                                Text(
                                    text = stringResource(R.string.action_retry),
                                    style = MaterialTheme.typography.bodyLarge.copy(
                                        fontWeight = FontWeight.W600,
                                        color = MaterialTheme.colorScheme.onPrimary,
                                    ),
                                )
                            }
                        }
                    }
                } else if (state.isLoading) {
                    CircularProgressIndicator()
                } else {
                    state.theme?.let { theme ->
                        ThemePreviewContent(
                            theme = theme,
                            modifier = Modifier.fillMaxSize(),
                            isApplying = state.isApplying,
                            isDownloadingWallpaper = isDownloadingWallpaper,
                        )
                    } ?: Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center,
                    ) {
                        Text(stringResource(R.string.unable_to_load_theme))
                    }
                }
            }
        }

        // Full screen loading overlay when downloading wallpaper
        if (isDownloadingWallpaper) {
            LoadingOverlay(
                description = stringResource(R.string.live_wallpaper_download_description),
                loadingProgress = downloadProgress,
            )
        }
    }
}

@SuppressLint("ConfigurationScreenWidthHeight")
@Composable
private fun ThemePreviewContent(
    theme: ThemeDTO,
    isApplying: Boolean,
    isDownloadingWallpaper: Boolean,
    modifier: Modifier = Modifier,
) {
    val isDarkTheme = isSelectedThemeDark
    val screenWidth = LocalConfiguration.current.screenWidthDp.dp

    Box(
        modifier = modifier.fillMaxSize(),
    ) {
        if (isDarkTheme) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .offset(y = 24.dp),
            ) {
                Box(
                    modifier = Modifier
                        .size(screenWidth)
                        .blur(100.dp, edgeTreatment = BlurredEdgeTreatment.Unbounded)
                        .background(Color.White.copy(alpha = 0.3f), shape = CircleShape),
                )
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize(),
        ) {
            Spacer(modifier = Modifier.height(8.dp))

            CarouselSlider(
                theme = theme,
                isApplying = isApplying,
                isDownloadingWallpaper = isDownloadingWallpaper,
                modifier = Modifier.weight(1f),
            )

            Spacer(modifier = Modifier.height(27.dp))
        }
    }
}

@Composable
private fun CarouselSlider(
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues = PaddingValues(horizontal = 16.dp),
    theme: ThemeDTO,
    isApplying: Boolean = false,
    isDownloadingWallpaper: Boolean = false,
) {
    val lazyListState = rememberLazyListState()

    TrackLazyListScroll(
        lazyListState = lazyListState,
        screenName = ScreenNames.THEME_PREVIEW,
        eventName = LogEventNames.BTN_IMAGE_SCROLL,
        params = mapOf(
            LogEventParams.THEME_ID to theme.id,
            LogEventParams.IS_VIDEO to theme.isLiveWallpaper,
        ),
    )

    LazyRow(
        state = lazyListState,
        modifier = modifier,
        contentPadding = contentPadding,
        horizontalArrangement = Arrangement.spacedBy(12.dp),
    ) {
        if (theme.images.isNotEmpty()) {
            item {
                ImagePreviewItem(
                    imageUrl = theme.images[0],
                    modifier = Modifier
                        .fillMaxHeight()
                        .aspectRatio(0.5625f),
                )
            }
        }

        theme.wallpaper?.videoUrl?.let { videoUrl ->
            if (videoUrl.isNotBlank()) {
                item {
                    VideoPreviewItem(
                        themeId = theme.id,
                        videoUrl = videoUrl,
                        isApplying = isApplying,
                        isDownloadingWallpaper = isDownloadingWallpaper,
                        modifier = Modifier
                            .fillMaxHeight()
                            .aspectRatio(0.5625f),
                    )
                }
            }
        }

        if (theme.images.size > 1) {
            items(theme.images.drop(1)) { imageUrl ->
                ImagePreviewItem(
                    imageUrl = imageUrl,
                    modifier = Modifier
                        .fillMaxHeight()
                        .aspectRatio(0.5625f),
                )
            }
        }
    }
}

@androidx.annotation.OptIn(UnstableApi::class)
@Composable
private fun VideoPreviewItem(
    themeId: Int,
    videoUrl: String,
    modifier: Modifier = Modifier,
    isApplying: Boolean = false,
    isDownloadingWallpaper: Boolean = false,
    borderRadius: Dp = 24.dp,
) {
    val context = LocalContext.current
    var isPlaying by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(true) }

    val exoPlayer = remember {
        ExoPlayer.Builder(context).build().apply {
            repeatMode = Player.REPEAT_MODE_ONE
            volume = 0f
            setMediaItem(MediaItem.fromUri(videoUrl))
            prepare()

            // Add listener to track loading state
            addListener(
                object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        isLoading = when (playbackState) {
                            Player.STATE_BUFFERING -> true
                            Player.STATE_READY -> false
                            Player.STATE_ENDED -> false
                            else -> isLoading
                        }
                    }
                },
            )
        }
    }

    DisposableEffect(exoPlayer) {
        onDispose {
            exoPlayer.release()
        }
    }

    LaunchedEffect(isPlaying) {
        if (isPlaying) {
            exoPlayer.play()
        } else {
            exoPlayer.pause()
        }
    }

    // Track lifecycle to pause video when app goes to background
    val resumed = lifecycleState().isAtLeast(Lifecycle.State.RESUMED)

    // Pause video when applying theme, downloading wallpaper, or app goes to background
    LaunchedEffect(isApplying, isDownloadingWallpaper, resumed) {
        if (isApplying || isDownloadingWallpaper || !resumed) {
            exoPlayer.pause()
            isPlaying = false
        }
    }

    val shape = RoundedCornerShape(borderRadius)
    Box(
        modifier = modifier.fillMaxSize(),
    ) {
        Card(
            modifier = Modifier
                .fillMaxSize()
                .dropShadow(
                    color = Color(0x406066CA),
                    shape = shape,
                    blur = 4.dp,
                    offsetY = 4.dp,
                    offsetX = 0.dp,
                    spread = 0.dp,
                )
                .dropShadow(
                    color = Color.Black.copy(alpha = 0.08f),
                    shape = shape,
                    blur = 60.dp,
                    offsetY = 4.dp,
                    offsetX = 0.dp,
                    spread = 0.dp,
                ),
            shape = shape,
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clickable(enabled = !isLoading) {
                        logger().logClickEvent(
                            screenName = ScreenNames.THEME_PREVIEW,
                            eventName = if (isPlaying) LogEventNames.BTN_PAUSE_VIDEO else LogEventNames.BTN_PLAY_VIDEO,
                            params = mapOf(
                                LogEventParams.THEME_ID to themeId,
                                LogEventParams.IS_VIDEO to true,
                            ),
                        )
                        isPlaying = !isPlaying
                    },
                contentAlignment = Alignment.Center,
            ) {
                // ExoPlayer VideoView
                AndroidView(
                    factory = { context ->
                        PlayerView(context).apply {
                            useController = false
                            controllerAutoShow = false
                            controllerHideOnTouch = false
                            controllerShowTimeoutMs = 0
                            hideController()
                            player = exoPlayer
                            resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
                        }
                    },
                    modifier = Modifier.fillMaxSize(),
                )

                // Loading indicator
                if (isLoading) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                color = Color.Black.copy(alpha = 0.5f),
                            ),
                        contentAlignment = Alignment.Center,
                    ) {
                        CircularProgressIndicator(
                            color = Color.White,
                            modifier = Modifier.size(45.dp),
                            strokeWidth = 3.dp,
                        )
                    }
                }

                // Play/Pause button overlay
                if (!isPlaying && !isLoading) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                color = Color.Black.copy(alpha = 0.5f),
                            ),
                        contentAlignment = Alignment.Center,
                    ) {
                        Image(
                            painter = painterResource(R.drawable.ic_play_video),
                            contentDescription = "Play pause button",
                            modifier = Modifier.size(45.dp),
                        )
                    }
                }
            }
        }
        Row(
            modifier = Modifier
                .padding(start = 11.dp, top = 15.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            LiveTag(
                iconSize = 14.22.dp,
                fontSize = 10.67.sp,
                lineHeight = 14.22.sp,
            )
        }
    }
}

@Composable
private fun ImagePreviewItem(
    imageUrl: String,
    modifier: Modifier = Modifier,
    borderRadius: Dp = 24.dp,
) {
    val shape = RoundedCornerShape(borderRadius)
    Card(
        modifier = modifier
            .fillMaxSize()
            .dropShadow(
                color = Color(0x406066CA),
                shape = shape,
                blur = 4.dp,
                offsetY = 4.dp,
                offsetX = 0.dp,
                spread = 0.dp,
            )
            .dropShadow(
                color = Color.Black.copy(alpha = 0.08f),
                shape = shape,
                blur = 60.dp,
                offsetY = 4.dp,
                offsetX = 0.dp,
                spread = 0.dp,
            ),
        shape = shape,
    ) {
        SubcomposeAsyncImage(
            model = imageUrl,
            contentDescription = stringResource(R.string.theme_image_preview_description),
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop,
            loading = {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    CircularProgressIndicator(
                        color = MaterialTheme.colorScheme.primary,
                    )
                }
            },
            error = {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color(0xFFE7F0FE)),
                    contentAlignment = Alignment.Center,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.load_image_error),
                        contentDescription = null,
                        Modifier.size(width = 106.dp, height = 92.58.dp),
                    )
                }
            },
        )
    }
}

val WindowSizeClass.itemWidth: Dp
    get() = when (this.widthSizeClass) {
        WindowWidthSizeClass.Compact -> 224.dp
        WindowWidthSizeClass.Medium -> 280.dp
        WindowWidthSizeClass.Expanded -> 320.dp
        else -> 224.dp
    }

@Composable
private fun BottomBarView(
    theme: ThemeDTO,
    isApplying: Boolean,
    isDownloadingWallpaper: Boolean,
    onClick: () -> Unit,
) {
    val enabled =
        !isApplying && !isDownloadingWallpaper

    val isDarkTheme = isSelectedThemeDark

    Box(
        modifier = Modifier
            .background(
                brush = if (isDarkTheme) {
                    Brush.verticalGradient(
                        colorStops = arrayOf(
                            0.2285f to Color(0xFF191919),
                            0.4643f to Color(0xFF1B1B1B),
                        ),
                    )
                } else {
                    Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFFFFFFFF).copy(alpha = 0.96f),
                            Color(0xFFEEF5FF).copy(alpha = 0.72f),
                        ),
                    )
                },
                shape =
                    RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp),
            )
            .clip(RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)),
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(top = 26.dp, bottom = 8.dp)
                .navigationBarsPadding(),
        ) {
            theme.iconPack?.let { iconPackInfo ->
                Text(
                    text = stringResource(R.string.icons_label),
                    style = MaterialTheme.typography.headlineMedium,
                    color = if (isDarkTheme) Color.White else Color.Black,
                )

                Spacer(modifier = Modifier.height(12.dp))

                Box(
                    modifier = Modifier
                        .background(
                            if (isDarkTheme) Color.White.copy(alpha = 0.05f) else Color.Black.copy(
                                alpha = 0.05f,
                            ),
                            shape = RoundedCornerShape(24.dp),
                        )
                        .border(
                            width = 1.dp,
                            color = if (isDarkTheme) Color(0xFF1C1C1C).copy(0.9f) else Color(
                                0xFFE2E2E9,
                            ).copy(0.9f),
                            shape = RoundedCornerShape(24.dp),
                        ),
                ) {
                    Row(
                        modifier = Modifier.padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        SubcomposeAsyncImage(
                            model = iconPackInfo.thumb,
                            contentDescription = null,
                            contentScale = ContentScale.Crop,
                            modifier = Modifier
                                .size(56.dp)
                                .clip(RoundedCornerShape(8.dp)),
                            loading = {
                                Box(
                                    modifier = Modifier.fillMaxSize(),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    CircularProgressIndicator(
                                        color = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.size(16.dp),
                                        strokeWidth = 2.dp,
                                    )
                                }
                            },
                            error = {
                                Box(
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .background(Color(0xFFE7F0FE)),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    Image(
                                        painter = painterResource(id = R.drawable.load_image_error),
                                        contentDescription = null,
                                        Modifier.size(
                                            width = 32.dp,
                                            height = 27.95.dp,
                                        ),
                                    )
                                }
                            },
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                iconPackInfo.name,
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontWeight = FontWeight.W500,
                                ),
                                color = if (isDarkTheme) Color(0xFFCCCCCC) else Color(0xFF333333),
                            )
                            Spacer(modifier = Modifier.height(2.dp))
                            Text(
                                text = stringResource(R.string.icon_pack_label),
                                style = MaterialTheme.typography.bodySmall.copy(
                                    fontWeight = FontWeight.W400,
                                    color = if (isDarkTheme) Color(0xFF666666) else Color(0xFF999999),
                                ),
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(15.dp))

            Box(
                modifier = Modifier
                    .height(48.dp)
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(40.dp))
                    .background(if (enabled) Color(0xFF0D68F1) else MaterialTheme.colorScheme.surfaceVariant)
                    .clickable(
                        enabled = enabled,
                        onClick = onClick,
                    )
                    .padding(vertical = 12.dp, horizontal = 24.dp),
                contentAlignment = Alignment.Center,
            ) {
                if (isApplying || isDownloadingWallpaper) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = MaterialTheme.colorScheme.onPrimary,
                        strokeWidth = 2.dp,
                    )
                } else {
                    Text(
                        text = stringResource(R.string.action_apply),
                        style = MaterialTheme.typography.bodyLarge.copy(
                            fontWeight = FontWeight.W600,
                            color = MaterialTheme.colorScheme.onPrimary,
                        ),
                    )
                }
            }
        }
    }
}
