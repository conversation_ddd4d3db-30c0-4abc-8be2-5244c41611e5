package com.live.wallpapers.widgets.themes.activities.themeintro

import android.app.Activity
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PageSize
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.lerp
import coil3.compose.SubcomposeAsyncImage
import com.android.launcher3.BuildConfig
import com.android.launcher3.R
import com.live.wallpapers.widgets.themes.analytics.logger
import com.live.wallpapers.widgets.themes.analytics.model.LogEventNames
import com.live.wallpapers.widgets.themes.analytics.model.LogEventParams
import com.live.wallpapers.widgets.themes.analytics.model.ScreenNames
import com.live.wallpapers.widgets.themes.data.remote.models.ThemeDTO
import com.live.wallpapers.widgets.themes.livewallpaper.LiveWallpaperHelper
import com.live.wallpapers.widgets.themes.livewallpaper.VideoWallpaperService
import com.live.wallpapers.widgets.themes.ui.bottomsheet.SetLiveWallpaperBottomSheetContent
import com.live.wallpapers.widgets.themes.ui.preferences.LocalWindowSizeClass
import com.live.wallpapers.widgets.themes.ui.preferences.components.AutoSizedText
import com.live.wallpapers.widgets.themes.ui.preferences.components.LoadingOverlay
import com.live.wallpapers.widgets.themes.ui.preferences.components.NoInternetConnectionView
import com.live.wallpapers.widgets.themes.ui.theme.isSelectedThemeDark
import com.live.wallpapers.widgets.themes.ui.util.BottomSheetOptions
import com.live.wallpapers.widgets.themes.ui.util.addIf
import com.live.wallpapers.widgets.themes.ui.util.bottomSheetHandler
import com.live.wallpapers.widgets.themes.util.rememberNetworkState
import kotlin.math.absoluteValue
import kotlin.math.max
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Màn hình giới thiệu theme cho người dùng mới
 */
@Composable
fun ThemeIntroScreen(
    viewModel: ThemeIntroViewModel,
) {
    val isDarkTheme = isSelectedThemeDark
    val context = LocalContext.current
    val bottomSheetHandler = bottomSheetHandler
    val coroutineScope = rememberCoroutineScope()

    val networkState by rememberNetworkState()
    val isConnected by remember(networkState) {
        derivedStateOf {
            networkState.isConnected()
        }
    }

    // Track previous network state to detect connectivity changes
    var previousNetworkState by remember { mutableStateOf(networkState) }

    // Collect UI state
    val uiState by viewModel.uiState.collectAsState()
    var showContent by remember { mutableStateOf(false) }
    var isDownloadingWallpaper by remember { mutableStateOf(false) }
    var downloadProgress by remember { mutableStateOf(0) }

    LaunchedEffect(Unit) {
        delay(100)
        showContent = true
    }

    LaunchedEffect(networkState) {
        if (!previousNetworkState.isConnected() && networkState.isConnected()) {
            viewModel.reloadThemes()
        }
        previousNetworkState = networkState
    }

    var isLiveWallpaperSet by remember { mutableStateOf(false) }

    val launcher =
        rememberLauncherForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                if (LiveWallpaperHelper.isLiveWallpaperSet(context)) {
                    if (isLiveWallpaperSet) {
                        VideoWallpaperService.sendUpdateVideoIntent(context)
                    }
                    viewModel.applyThemeWithoutWallpaper()
                } else {
                    Toast.makeText(
                        context,
                        R.string.theme_apply_error_without_message,
                        Toast.LENGTH_LONG,
                    ).show()
                    viewModel.setApplying(false)
                }
            } else {
                viewModel.setApplying(false)
            }
        }

    Box(
        modifier = Modifier
            .fillMaxSize(),
    ) {
        if (isConnected) {
            val wallpaperUrl = uiState.selectedTheme?.wallpaperUrl
            if (uiState.isOnDiscoveryPage) {
                Image(
                    painter = painterResource(R.drawable.img_store_discover_page),
                    contentDescription = "discover image",
                    modifier = Modifier
                        .fillMaxSize()
                        .graphicsLayer(alpha = 0.8f)
                        .blur(radius = 20.dp),
                    contentScale = ContentScale.Crop,
                )
            } else if (!wallpaperUrl.isNullOrBlank()) {
                SubcomposeAsyncImage(
                    model = wallpaperUrl,
                    contentDescription = stringResource(R.string.wallpaper_description),
                    modifier = Modifier
                        .fillMaxSize()
                        .graphicsLayer(alpha = 0.8f)
                        .blur(radius = 20.dp),
                    contentScale = ContentScale.Crop,
                    loading = {
                        DefaultWallpaper()
                    },
                    error = {
                        DefaultWallpaper()
                    },
                )
            }
        }

        if (isConnected) {
            if (isDarkTheme) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.1f)),
                )
            } else {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.White.copy(alpha = 0.1f)),
                )
            }

        }

        val selectedTheme = uiState.selectedTheme
        val selectedThemeId = selectedTheme?.id ?: 0
        val isLiveWallpaper = selectedTheme?.isLiveWallpaper == true

        Scaffold(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding(),
            containerColor = if (isConnected) Color.Transparent else Color.Unspecified,
            topBar = {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .padding(top = 12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    if (isConnected && uiState.error == null) {
                        Text(
                            text = stringResource(R.string.choose_theme_label),
                            style = MaterialTheme.typography.displayMedium.copy(
                                color = MaterialTheme.colorScheme.onBackground,
                            ),
                        )
                    } else {
                        Spacer(modifier = Modifier.weight(1f))
                    }


                    var containerColor = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.16f)
                    var contentColor = MaterialTheme.colorScheme.onBackground

                    if (!isConnected) {
                        containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                        contentColor = MaterialTheme.colorScheme.primary
                    }

                    TextButton(
                        onClick = {
                            viewModel.skipThemeSelection()
                        },
                        modifier = Modifier
                            .height(28.dp),
                        shape = RoundedCornerShape(16.dp),
                        colors = ButtonDefaults.textButtonColors(
                            containerColor = containerColor,
                            contentColor = contentColor,
                        ),
                        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp),
                        enabled = !uiState.isApplying && !isDownloadingWallpaper,
                    ) {
                        Text(
                            text = stringResource(R.string.action_skip),
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.W500,
                            ),
                        )
                    }
                }
            },
        ) { innerPadding ->
            val onApplyTheme: () -> Unit = {
                logger().logClickEvent(
                    screenName = ScreenNames.INTRO_THEME,
                    eventName = if (isLiveWallpaper) LogEventNames.BTN_APPLY_THEME_LIVE else LogEventNames.BTN_APPLY_THEME,
                    params = mapOf(
                        LogEventParams.THEME_ID to selectedThemeId,
                        LogEventParams.IS_VIDEO to isLiveWallpaper,
                    ),
                )

                if (isLiveWallpaper) {
                    logger().logClickEvent(
                        screenName = ScreenNames.INTRO_THEME,
                        eventName = LogEventNames.VIEW_LIVE_WARNING_POPUP,
                        params = mapOf(
                            LogEventParams.THEME_ID to selectedThemeId,
                            LogEventParams.IS_VIDEO to true,
                        ),
                    )
                    bottomSheetHandler.show(
                        BottomSheetOptions(
                            showDrag = false,
                            sheetModifier = Modifier
                                .padding(horizontal = 16.dp)
                                .padding(bottom = innerPadding.calculateBottomPadding() + 8.dp),
                        ) {
                            SetLiveWallpaperBottomSheetContent(
                                onClose = {
                                    logger().logClickEvent(
                                        screenName = ScreenNames.INTRO_THEME,
                                        eventName = LogEventNames.BTN_LIVE_WARNING_POPUP_CANCEL,
                                        params = mapOf(
                                            LogEventParams.THEME_ID to selectedThemeId,
                                            LogEventParams.IS_VIDEO to true,
                                        ),
                                    )
                                    bottomSheetHandler.hide()
                                },
                                onConfirm = {
                                    logger().logClickEvent(
                                        screenName = ScreenNames.INTRO_THEME,
                                        eventName = LogEventNames.BTN_LIVE_WARNING_POPUP_OK,
                                        params = mapOf(
                                            LogEventParams.THEME_ID to selectedThemeId,
                                            LogEventParams.IS_VIDEO to true,
                                        ),
                                    )

                                    bottomSheetHandler.hide()
                                    isDownloadingWallpaper = true
                                    downloadProgress = 0

                                    viewModel.downloadLiveWallpaperTheme(
                                        onProgress = { progress ->
                                            downloadProgress = progress
                                        },
                                        onComplete = { success, videoFile, thumbnailFile ->
                                            isDownloadingWallpaper = false
                                            if (success && videoFile != null) {
                                                coroutineScope.launch {
                                                    LiveWallpaperHelper.setLiveWallpaper(
                                                        context = context,
                                                        videoFile = videoFile,
                                                        thumbnailFile = thumbnailFile,
                                                        screenName = ScreenNames.INTRO_THEME,
                                                        onLaunchIntent = { intent ->
                                                            isLiveWallpaperSet =
                                                                LiveWallpaperHelper.isLiveWallpaperSet(
                                                                    context,
                                                                )

                                                            if (intent != null) {
                                                                launcher.launch(intent)
                                                            } else {
                                                                Toast.makeText(
                                                                    context,
                                                                    R.string.error_wallpaper_chooser,
                                                                    Toast.LENGTH_LONG,
                                                                ).show()
                                                                viewModel.setApplying(false)
                                                            }
                                                        },
                                                    )
                                                }
                                            }
                                        },
                                    )
                                },
                            )
                        },
                    )
                } else {
                    viewModel.applyThemeAndContinue()
                }
            }

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding),
            ) {
                if (isConnected && uiState.error == null) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.SpaceBetween,
                    ) {
                        if (uiState.isLoading) {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center,
                            ) {
                                CircularProgressIndicator(
                                    color = Color.White,
                                )
                            }
                        } else if (uiState.themes.isNotEmpty()) {
                            BoxWithConstraints(
                                modifier = Modifier.weight(1f),
                                contentAlignment = Alignment.Center,
                            ) {
                                val height = maxHeight - 220.dp
                                val aspectRatio = 9f / 16f
                                val baseItemWidth = max(height.value * aspectRatio, 180f)

                                CarouselSlider(
                                    items = uiState.themes,
                                    onSelectTheme = { theme ->
                                        viewModel.selectTheme(theme)
                                    },
                                    onSelectDiscovery = {
                                        viewModel.selectDiscoveryPage()
                                    },
                                    aspectRatio = aspectRatio,
                                    baseItemWidth = baseItemWidth,
                                )
                            }

                            AnimatedVisibility(
                                visible = showContent,
                                enter = fadeIn() + slideInVertically(
                                    initialOffsetY = { 100 },
                                    animationSpec = spring(
                                        dampingRatio = Spring.DampingRatioMediumBouncy,
                                        stiffness = Spring.StiffnessLow,
                                        visibilityThreshold = null,
                                    ),
                                ),
                            ) {
                                Button(
                                    onClick = {
                                        if (uiState.isOnDiscoveryPage) {
                                            viewModel.navigateToDiscovery()
                                        } else {
                                            onApplyTheme()
                                        }
                                    },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = 24.dp)
                                        .padding(top = 8.dp, bottom = 24.dp)
                                        .height(48.dp),
                                    shape = RoundedCornerShape(28.dp),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Color(0xFF0D68F1),
                                        contentColor = Color.White,
                                        disabledContainerColor = Color(0xFF595959),
                                        disabledContentColor = Color.White,
                                    ),
                                    enabled = (uiState.selectedTheme != null || uiState.isOnDiscoveryPage) && !uiState.isApplying && !isDownloadingWallpaper,
                                ) {
                                    if (uiState.isApplying || isDownloadingWallpaper) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(24.dp),
                                            color = Color.White,
                                            strokeWidth = 2.dp,
                                        )
                                    } else {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.Center,
                                        ) {
                                            Text(
                                                text = if (uiState.isOnDiscoveryPage) stringResource(
                                                    R.string.action_discover,
                                                ) else stringResource(
                                                    R.string.action_apply,
                                                ),
                                                fontSize = 16.sp,
                                                fontWeight = FontWeight.SemiBold,
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    Column(
                        modifier = Modifier
                            .fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        Box(
                            modifier = Modifier.weight(1f),
                            contentAlignment = Alignment.Center,
                        ) {
                            NoInternetConnectionView()
                        }

                        Box(
                            modifier = Modifier
                                .padding(horizontal = 16.dp, vertical = 8.dp)
                                .navigationBarsPadding(),
                        ) {
                            Box(
                                modifier = Modifier
                                    .height(48.dp)
                                    .fillMaxWidth()
                                    .clip(RoundedCornerShape(40.dp))
                                    .background(MaterialTheme.colorScheme.primary)
                                    .clickable(
                                        onClick = {
                                            if (!isConnected) {
                                                Toast.makeText(
                                                    context,
                                                    R.string.no_internet_connection_please_check_your_network,
                                                    Toast.LENGTH_SHORT,
                                                ).show()
                                            } else {
                                                viewModel.reloadThemes()
                                            }
                                        },
                                    )
                                    .padding(vertical = 12.dp, horizontal = 24.dp),
                                contentAlignment = Alignment.Center,
                            ) {
                                Text(
                                    text = stringResource(R.string.action_retry),
                                    style = MaterialTheme.typography.bodyLarge.copy(
                                        fontWeight = FontWeight.W600,
                                        color = MaterialTheme.colorScheme.onPrimary,
                                    ),
                                )
                            }
                        }
                    }
                }
            }
        }

        // Full screen loading overlay when downloading wallpaper
        if (isDownloadingWallpaper) {
            LoadingOverlay(
                description = stringResource(R.string.live_wallpaper_download_description),
                loadingProgress = downloadProgress,
            )
        }
    }
}

/**
 * CarouselSlider
 * @param items List of items to display in the carousel
 * @param modifier Modifier for the carousel container
 * @param onSelectTheme Callback when an item is selected
 * @param enlargeCenterPage Whether to enlarge the center page
 * @param aspectRatio Aspect ratio of each item (width / height)
 * @param baseItemWidth Base width in DP for calculating responsive viewport fraction
 */
@Composable
fun CarouselSlider(
    items: List<ThemeDTO>,
    modifier: Modifier = Modifier,
    onSelectTheme: (ThemeDTO) -> Unit = {},
    onSelectDiscovery: () -> Unit = {},
    enlargeCenterPage: Boolean = true,
    aspectRatio: Float = 9f / 16f,
    baseItemWidth: Float = 250f,
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp

    val viewportFraction = remember(screenWidth, baseItemWidth) {
        val fraction = baseItemWidth / screenWidth.value
        fraction.coerceIn(0.3f, 1.0f)
    }

    // Calculate content padding based on viewport fraction
    val contentPadding = remember(viewportFraction, screenWidth) {
        val sideMargin = (screenWidth.value * (1f - viewportFraction)) / 2f
        PaddingValues(horizontal = sideMargin.dp)
    }

    // Calculate page spacing
    val pageSpacing = remember(viewportFraction) {
        // Smaller spacing for larger viewport fractions (more items visible)
        if (viewportFraction < 0.6f) 16.dp else 8.dp
    }

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        // Total page count includes discovery page
        val totalPageCount = items.size + 1
        val pagerState = rememberPagerState(pageCount = { totalPageCount })

        // Check if current page is discovery page (last page)
        val isCurrentDiscoveryPage by remember(pagerState.currentPage) {
            derivedStateOf { pagerState.currentPage == items.size }
        }

        // Current theme based on page
        val currentTheme by remember(pagerState.currentPage) {
            derivedStateOf {
                if (pagerState.currentPage < items.size && items.isNotEmpty()) {
                    items[pagerState.currentPage]
                } else null
            }
        }

        // Update selected theme when page changes
        LaunchedEffect(pagerState.currentPage) {
            if (isCurrentDiscoveryPage) {
                onSelectDiscovery()
            } else {
                currentTheme?.let { onSelectTheme(it) }
            }
        }

        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxWidth(),
            contentPadding = contentPadding,
            pageSpacing = pageSpacing,
            pageSize = PageSize.Fill,
        ) { page ->
            if (page == items.size) {
                // Discovery page
                DiscoveryPageCard(
                    pagerState = pagerState,
                    currentPage = page,
                    aspectRatio = aspectRatio,
                    enlargeCenterPage = enlargeCenterPage,
                )
            } else {
                val theme = items.getOrNull(page)
                if (theme != null) {
                    CarouselItem(
                        theme = theme,
                        pagerState = pagerState,
                        currentPage = page,
                        aspectRatio = aspectRatio,
                        enlargeCenterPage = enlargeCenterPage,
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        PageIndicator(
            pagerState = pagerState,
        )
    }
}

@Composable
private fun DiscoveryPageCard(
    pagerState: PagerState,
    currentPage: Int,
    aspectRatio: Float,
    enlargeCenterPage: Boolean,
) {
    // Calculate page offset for scale animation
    val pageOffset = calculatePageOffset(
        currentPage = pagerState.currentPage,
        currentPageOffset = pagerState.currentPageOffsetFraction,
        index = currentPage,
    )

    // Scale animation
    val scale = if (enlargeCenterPage) {
        lerp(
            start = 0.85f,
            stop = 1.0f,
            fraction = 1f - pageOffset.coerceIn(0f, 1f),
        )
    } else {
        1.0f
    }

    // Border width for selected item
    val borderWidth = animateDpAsState(
        targetValue = if (pageOffset < 0.1) 2.dp else 0.dp,
        label = "Border Width",
    )

    BoxWithConstraints(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(aspectRatio)
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
            .clip(RoundedCornerShape(24.dp))
            .border(
                width = borderWidth.value,
                color = Color.White,
                shape = RoundedCornerShape(24.dp),
            ),
    ) {
        val scaleFactor = calculateResponsiveScaleFactor(aspectRatio, maxWidth)

        Card(
            modifier = Modifier.fillMaxSize(),
            shape = RoundedCornerShape(24.dp),
            elevation = CardDefaults.cardElevation(
                defaultElevation = if (pageOffset < 0.5) 8.dp else 4.dp,
            ),
        ) {
            Image(
                painter = painterResource(id = R.drawable.img_store_discover_page),
                contentDescription = "Discovery",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop,
            )
        }

        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(
                    start = (24.dp * scaleFactor),
                    end = (25.dp * scaleFactor),
                    bottom = (80.dp * scaleFactor),
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            AutoSizedText(
                text = stringResource(R.string.explore_theme_title),
                style = TextStyle(
                    fontSize = (20.sp * scaleFactor),
                    lineHeight = (24.sp * scaleFactor),
                    fontWeight = FontWeight.W700,
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color(0xFF6275FF),
                            Color(0xFF3CFF52),
                        ),
                    ),
                ),
                textAlign = TextAlign.Center,
                maxLines = 2,
            )
            Spacer(modifier = Modifier.height(6.dp * scaleFactor))
            AutoSizedText(
                text = stringResource(R.string.explore_theme_description),
                style = TextStyle(
                    fontSize = (12.sp * scaleFactor),
                    lineHeight = (16.sp * scaleFactor),
                    fontWeight = FontWeight.W400,
                ),
                color = Color.White,
                maxLines = 1,
            )
        }
    }
}

@Composable
private fun CarouselItem(
    theme: ThemeDTO,
    pagerState: PagerState,
    currentPage: Int,
    aspectRatio: Float,
    enlargeCenterPage: Boolean,
) {
    val windowSizeClass = LocalWindowSizeClass.current
    val isLargeScreen = windowSizeClass.widthSizeClass != WindowWidthSizeClass.Compact

    // Calculate page offset for animations
    val pageOffset =
        ((pagerState.currentPage - currentPage) + pagerState.currentPageOffsetFraction).absoluteValue

    // Animate scale based on page offset
    val scale = if (enlargeCenterPage) {
        lerp(
            start = 0.85f,
            stop = 1.0f,
            fraction = 1f - pageOffset.coerceIn(0f, 1f),
        )
    } else {
        1.0f
    }

    // Border width for selected item
    val borderWidth = animateDpAsState(
        targetValue = if (pageOffset < 0.1) 2.dp else 0.dp,
        label = "Border Width",
    )

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(aspectRatio)
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
            .clip(RoundedCornerShape(24.dp))
            .border(
                width = borderWidth.value,
                color = Color.White,
                shape = RoundedCornerShape(24.dp),
            ),
    ) {
        Card(
            modifier = Modifier.fillMaxSize(),
            shape = RoundedCornerShape(24.dp),
            elevation = CardDefaults.cardElevation(
                defaultElevation = if (pageOffset < 0.5) 8.dp else 4.dp,
            ),
        ) {
            SubcomposeAsyncImage(
                model = theme.preview,
                contentDescription = stringResource(R.string.theme_image_preview_description),
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop,
                loading = {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center,
                    ) {
                        CircularProgressIndicator(
                            color = MaterialTheme.colorScheme.primary,
                        )
                    }
                },
                error = {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color(0xFFE7F0FE)),
                        contentAlignment = Alignment.Center,
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.load_image_error),
                            contentDescription = null,
                            modifier = Modifier.size(width = 106.dp, height = 92.58.dp),
                        )
                    }
                },
            )
        }

        // Live wallpaper badge
        if (theme.isLiveWallpaper) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(12.dp),
            ) {
                Box(
                    modifier = Modifier
                        .background(
                            color = Color.Black.copy(alpha = 0.5f),
                            shape = RoundedCornerShape(50),
                        )
                        .padding(horizontal = 5.dp, vertical = 2.dp)
                        .addIf(isLargeScreen) {
                            padding(horizontal = 3.dp, vertical = 2.dp)
                        },
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Image(
                            painterResource(R.drawable.ic_live_gradient2),
                            contentDescription = null,
                            modifier = Modifier.size(
                                if (isLargeScreen) 16.dp else 10.67.dp,
                            ),
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = stringResource(R.string.live_wallpaper_label),
                            style = TextStyle(
                                fontSize = if (isLargeScreen) 12.sp else 8.sp,
                                lineHeight = if (isLargeScreen) 16.sp else 10.67.sp,
                                fontWeight = FontWeight.W500,
                                brush = Brush.linearGradient(
                                    colors = listOf(
                                        Color(0xFF63FFED),
                                        Color(0xFF0677FF),
                                    ),
                                ),
                            ),
                        )
                    }
                }
            }
        }

        // Debug info
        if (BuildConfig.DEBUG) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp)
                    .background(Color.Yellow),
            ) {
                Text("${theme.id} - ${theme.iconPackId}", color = Color.Red)
            }
        }
    }
}

@Composable
private fun PageIndicator(
    pagerState: PagerState,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        repeat(pagerState.pageCount) { index ->
            // Calculate distance from current page
            val pageOffset = calculatePageOffset(
                currentPage = pagerState.currentPage,
                currentPageOffset = pagerState.currentPageOffsetFraction,
                index = index,
            )

            // Calculate width based on page offset - smooth transition from 24dp to 6dp
            val width = lerp(
                start = 24.dp,
                stop = 6.dp,
                fraction = (pageOffset * 2).coerceIn(0f, 1f),
            )

            // Calculate alpha based on page offset - smooth transition from 1f to 0.2f
            val alpha = lerp(
                start = 1f,
                stop = 0.2f,
                fraction = pageOffset.coerceIn(0f, 1f),
            )

            // Indicator dot
            Box(
                modifier = Modifier
                    .padding(horizontal = 4.dp)
                    .clip(RoundedCornerShape(3.dp))
                    .background(Color.White.copy(alpha = alpha))
                    .width(width)
                    .height(6.dp),
            )
        }
    }
}

// Helper functions
private fun calculatePageOffset(
    currentPage: Int,
    currentPageOffset: Float,
    index: Int,
): Float {
    val distance = (currentPage + currentPageOffset - index).absoluteValue
    return distance.coerceIn(0f, 1f)
}

/**
 * Linear interpolation between start and stop based on fraction
 */
private fun lerp(start: Dp, stop: Dp, fraction: Float): Dp {
    return start + ((stop - start) * fraction)
}

@Composable
private fun DefaultWallpaper(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black),
    )
}

/**
 * Calculate responsive scale factor based on current card size vs reference design (310dp x 550dp)
 */
@Composable
private fun calculateResponsiveScaleFactor(aspectRatio: Float, cardWidth: Dp): Float {
    // Calculate current card dimensions based on carousel logic
    val cardHeight = cardWidth / aspectRatio

    // Reference design dimensions
    val referenceWidth = 310.dp
    val referenceHeight = 550.dp

    // Calculate scale factors for width and height
    val widthScale = cardWidth / referenceWidth
    val heightScale = cardHeight / referenceHeight

    // Use average scale factor for balanced scaling
    val scaleFactor = (widthScale + heightScale) / 2f

    // Clamp the scale factor to reasonable bounds
    return scaleFactor.coerceIn(0.5f, 1.5f)
}
