package com.live.wallpapers.widgets.themes.activities.themeintro

import android.app.Application
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.android.launcher3.R
import com.live.wallpapers.widgets.themes.analytics.logger
import com.live.wallpapers.widgets.themes.analytics.model.LogEventNames
import com.live.wallpapers.widgets.themes.analytics.model.LogEventParams
import com.live.wallpapers.widgets.themes.analytics.model.ScreenNames
import com.live.wallpapers.widgets.themes.config.RemoteConfigHelper
import com.live.wallpapers.widgets.themes.data.remote.models.IconPackConfigDTO
import com.live.wallpapers.widgets.themes.data.remote.models.ThemeDTO
import com.live.wallpapers.widgets.themes.data.remote.models.toEntity
import com.live.wallpapers.widgets.themes.data.repository.ThemesRepository
import com.live.wallpapers.widgets.themes.icons.IconPackDownloadHelper
import com.live.wallpapers.widgets.themes.livewallpaper.LiveWallpaperHelper
import com.live.wallpapers.widgets.themes.preferences.PreferenceManager
import com.live.wallpapers.widgets.themes.preferences2.PreferenceManager2
import com.live.wallpapers.widgets.themes.util.downloadWallpaperToFile
import com.live.wallpapers.widgets.themes.util.setWallpaperFromFile
import java.io.File
import java.net.UnknownHostException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Represents the UI state for the Theme Intro screen
 */
data class ThemeIntroUiState(
    val themes: List<ThemeDTO> = emptyList(),
    val selectedTheme: ThemeDTO? = null,
    val themeDetail: ThemeDTO? = null,
    val isLoading: Boolean = true,
    val isApplying: Boolean = false,
    val error: String? = null,
    val isOnDiscoveryPage: Boolean = false,
)

private const val TAG = "ThemeIntroViewModel"

class ThemeIntroViewModel(
    private val application: Application,
) : AndroidViewModel(application) {

    private val themesRepository = ThemesRepository.INSTANCE.get(application)
    private val preferenceManager = PreferenceManager.getInstance(application)
    private val prefs = PreferenceManager2.getInstance(application)

    // UI state
    private val _uiState = MutableStateFlow(ThemeIntroUiState())
    val uiState: StateFlow<ThemeIntroUiState> = _uiState.asStateFlow()

    // Navigation events
    private val _navigationEvent = MutableSharedFlow<NavigationEvent>()
    val navigationEvent: SharedFlow<NavigationEvent> = _navigationEvent.asSharedFlow()

    init {
        logger().logViewEvent(
            screenName = ScreenNames.INTRO_THEME,
            eventName = LogEventNames.VIEW_INTRO_THEME,
        )
        loadThemes()
    }

    fun reloadThemes() {
        if (_uiState.value.isLoading || _uiState.value.themes.isNotEmpty()) {
            return
        }

        loadThemes()
    }

    private fun loadThemes() {
        viewModelScope.launch {
            logger().logClickEvent(
                screenName = ScreenNames.INTRO_THEME,
                eventName = LogEventNames.LOADING_DATA,
            )
            _uiState.update { it.copy(isLoading = true, error = null) }
            try {
                val themeIds = RemoteConfigHelper.getInstance().getIntroThemeListIds()
                val response = themesRepository.getAllThemes(
                    listId = themeIds,
                    limit = themeIds.size,
                )

                logger().logClickEvent(
                    screenName = ScreenNames.INTRO_THEME,
                    eventName = LogEventNames.LOADING_DATA_SUCCESS,
                )

                // Sort themes according to the order in themeIds
                val sortedThemes = response.data.sortedBy { theme -> themeIds.indexOf(theme.id) }

                _uiState.update { currentState ->
                    val selectedTheme =
                        if (currentState.selectedTheme == null && sortedThemes.isNotEmpty()) {
                            sortedThemes.first()
                        } else {
                            currentState.selectedTheme
                        }

                    currentState.copy(
                        themes = sortedThemes,
                        selectedTheme = selectedTheme,
                        isLoading = false,
                        error = null,
                        isOnDiscoveryPage = false,
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
                logger().logClickEvent(
                    screenName = ScreenNames.INTRO_THEME,
                    eventName = LogEventNames.LOADING_DATA_ERROR,
                )
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        error = e.localizedMessage ?: "Unknown error occurred",
                    )
                }
            }
        }
    }

    /**
     * Update the selected theme
     */
    fun selectTheme(theme: ThemeDTO) {
        val currentTheme = _uiState.value.selectedTheme
        if (currentTheme?.id != theme.id) {
            logger().logClickEvent(
                screenName = ScreenNames.INTRO_THEME,
                eventName = LogEventNames.SCROLL_THEME,
                params = mapOf(
                    LogEventParams.THEME_ID to theme.id,
                    LogEventParams.IS_VIDEO to theme.isLiveWallpaper,
                ),
            )
        }

        _uiState.update { it.copy(selectedTheme = theme, isOnDiscoveryPage = false) }
    }

    /**
     * Set current page to discovery page
     */
    fun selectDiscoveryPage() {
        logger().logClickEvent(
            screenName = ScreenNames.INTRO_THEME,
            eventName = LogEventNames.SCROLL_DISCOVER,
        )
        _uiState.update { it.copy(selectedTheme = null, isOnDiscoveryPage = true) }
    }

    /**
     * Navigate to ThemePackActivity for discovery
     */
    fun navigateToDiscovery() {
        logger().logClickEvent(
            screenName = ScreenNames.INTRO_THEME,
            eventName = LogEventNames.BTN_DISCOVER_THEME,
        )
        viewModelScope.launch {
            _navigationEvent.emit(NavigationEvent.NavigateToThemePack)
        }
    }

    private suspend fun downloadIconPackIfNeeded(
        params: Map<String, Any> = emptyMap(),
        iconPackConfig: IconPackConfigDTO?,
        onProgress: ((Int) -> Unit)? = null,
    ): Boolean {
        return try {
            if (iconPackConfig != null) {
                IconPackDownloadHelper.downloadIconPack(
                    context = application,
                    iconPackConfig = iconPackConfig,
                    screenName = ScreenNames.INTRO_THEME,
                    params = params,
                    onProgress = onProgress,
                )
            } else {
                onProgress?.invoke(100)
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading icon pack", e)
            false
        }
    }

    private suspend fun getThemeDetail(): ThemeDTO? {
        val selectedTheme = _uiState.value.selectedTheme ?: return null
        val selectedThemeId = selectedTheme.id
        val currentDetail = _uiState.value.themeDetail

        val themeDetail = if (currentDetail?.id != selectedTheme.id) {
            Log.d(TAG, "Getting theme detail for ID: ${selectedTheme.id}")
            logger().logClickEvent(
                screenName = ScreenNames.INTRO_THEME,
                eventName = LogEventNames.LOADING_THEME,
                params = mapOf(
                    LogEventParams.THEME_ID to selectedThemeId,
                ),
            )
            val newTheme = themesRepository.getThemeDetail(
                themeId = selectedThemeId,
            )
            logger().logClickEvent(
                screenName = ScreenNames.INTRO_THEME,
                eventName = if (newTheme != null) LogEventNames.LOADING_THEME_SUCCESS else LogEventNames.LOADING_THEME_ERROR,
                params = mapOf(
                    LogEventParams.THEME_ID to selectedThemeId,
                ),
            )
            newTheme
        } else {
            currentDetail
        }

        themeDetail?.iconPack?.let { iconPack ->
            themesRepository.insertIconPack(iconPack.toEntity())
        }

        return themeDetail
    }

    /**
     * Navigate to home screen after theme setup
     */
    fun applyThemeAndContinue() {
        val selectedTheme = _uiState.value.selectedTheme ?: return
        val themeId = selectedTheme.id
        var iconPackId = selectedTheme.iconPackId
        val wallpaperId = selectedTheme.wallpaperId
        val isLiveWallpaper = selectedTheme.isLiveWallpaper

        viewModelScope.launch {
            // Set applying state to true
            _uiState.update { it.copy(isApplying = true) }
            try {
                val themeDetail = getThemeDetail()
                if (themeDetail == null) {
                    logger().logClickEvent(
                        screenName = ScreenNames.INTRO_THEME,
                        eventName = LogEventNames.BTN_APPLY_THEME_ERROR,
                        params = mapOf(
                            LogEventParams.THEME_ID to themeId,
                            LogEventParams.ICON_PACK_ID to iconPackId,
                            LogEventParams.WALLPAPER_ID to wallpaperId,
                            LogEventParams.IS_VIDEO to isLiveWallpaper,
                        ),
                    )
                    withContext(Dispatchers.Main) {
                        Toast.makeText(
                            application,
                            R.string.theme_apply_error_without_message,
                            Toast.LENGTH_SHORT,
                        ).show()
                    }
                    return@launch
                }
                iconPackId = themeDetail.iconPackId

                val logParams = mapOf(
                    LogEventParams.THEME_ID to themeId,
                    LogEventParams.ICON_PACK_ID to iconPackId,
                    LogEventParams.WALLPAPER_ID to wallpaperId,
                    LogEventParams.IS_VIDEO to isLiveWallpaper,
                )

                Log.d(TAG, "Theme detail loaded: ${themeDetail.name}")
                val startTime = System.currentTimeMillis()

                val wallpaperDeferred = viewModelScope.async(Dispatchers.IO) {
                    downloadWallpaperToFile(application, themeDetail.wallpaperUrl)
                }

                val iconPackDeferred = viewModelScope.async(Dispatchers.IO) {
                    downloadIconPackIfNeeded(
                        params = logParams,
                        iconPackConfig = themeDetail.iconPack?.config,
                    )
                }

                val wallpaperFile = wallpaperDeferred.await()
                val iconPackDownloaded = iconPackDeferred.await()

                val endTime = System.currentTimeMillis()
                val diff = endTime - startTime

                Log.d(TAG, "Theme downloaded in ${diff}ms")

                if (iconPackDownloaded) {
                    withContext(Dispatchers.IO) {
                        setWallpaperFromFile(application, wallpaperFile)
                    }

                    preferenceManager.iconPackPackage.set(
                        IconPackDownloadHelper.getPackageName(
                            themeDetail.iconPackId,
                        ),
                    )

                    logger().logClickEvent(
                        screenName = ScreenNames.INTRO_THEME,
                        eventName = LogEventNames.BTN_APPLY_THEME_SUCCESS,
                        params = logParams,
                    )
                    withContext(Dispatchers.Main) {
                        Toast.makeText(
                            application,
                            R.string.theme_applied_successfully,
                            Toast.LENGTH_SHORT,
                        ).show()
                    }
                } else {
                    logger().logClickEvent(
                        screenName = ScreenNames.INTRO_THEME,
                        eventName = LogEventNames.BTN_APPLY_THEME_ERROR,
                        params = logParams,
                    )
                    withContext(Dispatchers.Main) {
                        Toast.makeText(
                            application,
                            R.string.download_icon_pack_error,
                            Toast.LENGTH_SHORT,
                        ).show()
                    }
                    return@launch
                }

                themeDetail.wallpaper?.textColor?.let { color ->
                    prefs.homeTextColor.set(color)
                }

                _navigationEvent.emit(NavigationEvent.NavigateToSetDefault)
            } catch (e: Exception) {
                e.printStackTrace()
                logger().logClickEvent(
                    screenName = ScreenNames.INTRO_THEME,
                    eventName = LogEventNames.BTN_APPLY_THEME_ERROR,
                    params = mapOf(
                        LogEventParams.THEME_ID to themeId,
                        LogEventParams.ICON_PACK_ID to iconPackId,
                        LogEventParams.WALLPAPER_ID to wallpaperId,
                        LogEventParams.IS_VIDEO to isLiveWallpaper,
                    ),
                )
                val errorMessage =
                    if (e is UnknownHostException || e is coil3.network.HttpException) {
                        application.getString(R.string.no_internet_connection_please_check_your_network)
                    } else {
                        application.getString(R.string.theme_apply_error, e.localizedMessage)
                    }
                withContext(Dispatchers.Main) {
                    Toast.makeText(
                        application,
                        errorMessage,
                        Toast.LENGTH_SHORT,
                    ).show()
                }
            } finally {
                _uiState.update { it.copy(isApplying = false) }
            }
        }
    }

    fun setApplying(value: Boolean) {
        _uiState.update { it.copy(isApplying = value) }
    }

    /**
     * Apply theme without setting wallpaper (used when returning from live wallpaper setup)
     */
    fun applyThemeWithoutWallpaper() {
        Log.d(TAG, "applyThemeWithoutWallpaper")
        val selectedTheme = _uiState.value.selectedTheme ?: return
        val themeId = selectedTheme.id
        var iconPackId = selectedTheme.iconPackId
        val wallpaperId = selectedTheme.wallpaperId
        val isLiveWallpaper = selectedTheme.isLiveWallpaper

        viewModelScope.launch {
            // Set applying state to true
            _uiState.update { it.copy(isApplying = true) }

            try {
                val themeDetail = getThemeDetail()
                if (themeDetail == null) {
                    logger().logClickEvent(
                        screenName = ScreenNames.INTRO_THEME,
                        eventName = LogEventNames.BTN_APPLY_THEME_LIVE_ERROR,
                        params = mapOf(
                            LogEventParams.THEME_ID to themeId,
                            LogEventParams.ICON_PACK_ID to iconPackId,
                            LogEventParams.WALLPAPER_ID to wallpaperId,
                            LogEventParams.IS_VIDEO to isLiveWallpaper,
                        ),
                    )
                    withContext(Dispatchers.Main) {
                        Toast.makeText(
                            application,
                            R.string.theme_apply_error_without_message,
                            Toast.LENGTH_SHORT,
                        ).show()
                    }
                    return@launch
                }

                iconPackId = themeDetail.iconPackId

                val logParams = mapOf(
                    LogEventParams.THEME_ID to themeId,
                    LogEventParams.ICON_PACK_ID to iconPackId,
                    LogEventParams.WALLPAPER_ID to wallpaperId,
                    LogEventParams.IS_VIDEO to isLiveWallpaper,
                )

                Log.d(TAG, "Theme detail for apply without wallpaper loaded: ${themeDetail.name}")
                delay(1000)

                // Download icon pack first
                val iconPackDownloaded = withContext(Dispatchers.IO) {
                    downloadIconPackIfNeeded(
                        params = logParams,
                        iconPackConfig = themeDetail.iconPack?.config,
                    )
                }

                if (iconPackDownloaded) {
                    // Apply icon pack
                    preferenceManager.iconPackPackage.set(
                        IconPackDownloadHelper.getPackageName(
                            themeDetail.iconPackId,
                        ),
                    )

                    logger().logClickEvent(
                        screenName = ScreenNames.INTRO_THEME,
                        eventName = LogEventNames.BTN_APPLY_THEME_LIVE_SUCCESS,
                        params = logParams,
                    )
                    withContext(Dispatchers.Main) {
                        Toast.makeText(
                            application,
                            R.string.theme_applied_successfully,
                            Toast.LENGTH_SHORT,
                        ).show()
                    }
                } else {
                    logger().logClickEvent(
                        screenName = ScreenNames.INTRO_THEME,
                        eventName = LogEventNames.BTN_APPLY_THEME_LIVE_ERROR,
                        params = logParams,
                    )
                    withContext(Dispatchers.Main) {
                        Toast.makeText(
                            application,
                            R.string.download_icon_pack_error,
                            Toast.LENGTH_SHORT,
                        ).show()
                    }
                    return@launch
                }

                themeDetail.wallpaper?.textColor?.let { color ->
                    prefs.homeTextColor.set(color)
                }

                // Navigate to set default screen
                _navigationEvent.emit(NavigationEvent.NavigateToSetDefault)
            } catch (e: Exception) {
                e.printStackTrace()
                logger().logClickEvent(
                    screenName = ScreenNames.INTRO_THEME,
                    eventName = LogEventNames.BTN_APPLY_THEME_LIVE_ERROR,
                    params = mapOf(
                        LogEventParams.THEME_ID to themeId,
                        LogEventParams.ICON_PACK_ID to iconPackId,
                        LogEventParams.WALLPAPER_ID to wallpaperId,
                        LogEventParams.IS_VIDEO to isLiveWallpaper,
                    ),
                )
                val errorMessage = if (e is UnknownHostException) {
                    application.getString(R.string.no_internet_connection_please_check_your_network)
                } else {
                    application.getString(R.string.theme_apply_error, e.localizedMessage)
                }
                withContext(Dispatchers.Main) {
                    Toast.makeText(
                        application,
                        errorMessage,
                        Toast.LENGTH_SHORT,
                    ).show()
                }
            } finally {
                _uiState.update { it.copy(isApplying = false) }
            }
        }
    }

    /**
     * Download both live wallpaper and icon pack simultaneously for live wallpaper themes
     */
    fun downloadLiveWallpaperTheme(
        onProgress: (Int) -> Unit,
        onComplete: (Boolean, File?, File?) -> Unit,
    ) {
        if (_uiState.value.selectedTheme == null) return

        viewModelScope.launch {
            _uiState.update { it.copy(isApplying = true) }

            try {
                val themeDetail = getThemeDetail()
                if (themeDetail == null) {
                    withContext(Dispatchers.Main) {
                        Toast.makeText(
                            application,
                            R.string.theme_apply_error_without_message,
                            Toast.LENGTH_SHORT,
                        ).show()
                    }
                    onComplete(false, null, null)
                    return@launch
                }

                val logParams = mapOf(
                    LogEventParams.THEME_ID to themeDetail.id,
                    LogEventParams.ICON_PACK_ID to themeDetail.iconPackId,
                    LogEventParams.WALLPAPER_ID to themeDetail.wallpaperId,
                    LogEventParams.IS_VIDEO to themeDetail.isLiveWallpaper,
                )

                Log.d(TAG, "Live wallpaper theme detail loaded: ${themeDetail.name}")
                val startTime = System.currentTimeMillis()

                // Track progress for both downloads
                var iconPackProgress = 0
                var liveWallpaperProgress = 0

                fun updateCombinedProgress() {
                    val combined = (iconPackProgress + liveWallpaperProgress) / 2
                    onProgress(combined)
                }

                val iconPackDeferred = viewModelScope.async(Dispatchers.IO) {
                    downloadIconPackIfNeeded(
                        params = logParams,
                        iconPackConfig = themeDetail.iconPack?.config,
                    ) { progress ->
                        iconPackProgress = progress
                        updateCombinedProgress()
                    }
                }

                val liveWallpaperDeferred = viewModelScope.async(Dispatchers.IO) {
                    LiveWallpaperHelper.downloadLiveWallpaperFile(
                        context = application,
                        url = themeDetail.videoWallpaperUrl!!,
                        thumbnailUrl = themeDetail.wallpaperUrl,
                        screenName = ScreenNames.INTRO_THEME,
                        params = logParams,
                        onProgress = { progress ->
                            liveWallpaperProgress = progress
                            updateCombinedProgress()
                        },
                    )
                }

                val iconPackDownloaded = iconPackDeferred.await()
                val wallpaperDownloaded = liveWallpaperDeferred.await()

                val endTime = System.currentTimeMillis()
                val diff = endTime - startTime

                Log.d(TAG, "Live theme downloaded in ${diff}ms")

                if (iconPackDownloaded) {
                    onComplete(true, wallpaperDownloaded.first, wallpaperDownloaded.second)
                } else {
                    throw Exception("Download icon pack error")
                }
            } catch (e: Exception) {
                e.printStackTrace()
                val errorMessage = if (e is UnknownHostException) {
                    R.string.no_internet_connection_please_check_your_network
                } else {
                    R.string.download_theme_error
                }
                withContext(Dispatchers.Main) {
                    Toast.makeText(
                        application,
                        errorMessage,
                        Toast.LENGTH_SHORT,
                    ).show()
                    onComplete(false, null, null)
                }
                _uiState.update { it.copy(isApplying = false) }
            }
        }
    }

    /**
     * Skip theme setup and go to home screen
     */
    fun skipThemeSelection() {
        logger().logClickEvent(
            screenName = ScreenNames.INTRO_THEME,
            eventName = LogEventNames.BTN_SKIP,
        )
        viewModelScope.launch {
            _navigationEvent.emit(NavigationEvent.NavigateToSetDefault)
        }
    }

    /**
     * Navigation events that can occur in the theme selection screen
     */
    sealed class NavigationEvent {
        data object NavigateToHome : NavigationEvent()
        data object NavigateToSetDefault : NavigationEvent()
        data object NavigateToThemePack : NavigationEvent()
    }
}
