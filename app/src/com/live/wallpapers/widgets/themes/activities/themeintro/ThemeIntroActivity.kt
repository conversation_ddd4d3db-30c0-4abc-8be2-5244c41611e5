package com.live.wallpapers.widgets.themes.activities.themeintro

import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.material3.windowsizeclass.ExperimentalMaterial3WindowSizeClassApi
import androidx.compose.material3.windowsizeclass.calculateWindowSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.hk.base.ads.provider.common.updateLanguage
import com.hk.base.ads.provider.config.AdManagerProvider
import com.hk.base.ads.provider.inter.InterLoadManager
import com.live.wallpapers.widgets.themes.AILauncher
import com.live.wallpapers.widgets.themes.activities.setdefault.SetDefaultActivity
import com.live.wallpapers.widgets.themes.activities.themepack.ThemePackActivity
import com.live.wallpapers.widgets.themes.analytics.model.ScreenNames
import com.live.wallpapers.widgets.themes.ui.preferences.LocalWindowSizeClass
import com.live.wallpapers.widgets.themes.ui.theme.AppTheme
import com.live.wallpapers.widgets.themes.ui.theme.EdgeToEdge
import com.live.wallpapers.widgets.themes.ui.util.ProvideBottomSheetHandler
import com.live.wallpapers.widgets.themes.util.ProvideLifecycleState
import kotlinx.coroutines.launch

class ThemeIntroActivity : AppCompatActivity() {
    companion object {
        const val SHOULD_SHOW_AD_EXTRA = "SHOULD_SHOW_AD_EXTRA"
    }

    private val viewModel: ThemeIntroViewModel by viewModels()

    private lateinit var interLoadManager: InterLoadManager

    // Activity result launcher for ThemePackActivity
    private val themePackLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult(),
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            // User successfully applied theme from ThemePackActivity, navigate to SetDefault
            navigateToSetDefault(true)
        }
    }

    @OptIn(ExperimentalMaterial3WindowSizeClassApi::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        interLoadManager = AdManagerProvider.getInstance().startInterLoadManager
        interLoadManager.setScreen(ScreenNames.INTRO_THEME)
        // Tự động load sau khi hết màn start page, k cần gọi load
//        interLoadManager.loadInter(null)

        updateLanguage()
        enableEdgeToEdge()

        setContent {
            AppTheme {
                EdgeToEdge()
                Providers {
                    val windowSizeClass = calculateWindowSizeClass(this)
                    CompositionLocalProvider(
                        LocalWindowSizeClass provides windowSizeClass,
                    ) {
                        ThemeIntroScreen(
                            viewModel = viewModel,
                        )
                    }
                }
            }
        }

        // Observe navigation events using Flow
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.navigationEvent.collect { event ->
                    when (event) {
                        is ThemeIntroViewModel.NavigationEvent.NavigateToSetDefault -> {
                            navigateToSetDefault(false)
                        }

                        ThemeIntroViewModel.NavigationEvent.NavigateToHome -> {
                            navigateToLauncher()
                        }

                        ThemeIntroViewModel.NavigationEvent.NavigateToThemePack -> {
                            navigateToThemePack()
                        }
                    }
                }
            }
        }
    }

    private fun navigateToLauncher() {
        val intent = Intent(this, AILauncher::class.java)
        startActivity(intent)
        finish() // Close this activity
    }

    private fun navigateToSetDefault(fromThemePack: Boolean) {
        fun toSetDefault() {
            val intent = Intent(this, SetDefaultActivity::class.java).apply {
                // Hiện tại không cần show thêm extra ad
//                putExtra(SHOULD_SHOW_AD_EXTRA, fromThemePack)
                putExtra(SHOULD_SHOW_AD_EXTRA, false)
            }
            startActivity(intent)
            finish() // Close this activity
        }

        if (fromThemePack) {
            toSetDefault()
        } else {
            interLoadManager.showInter(this) {
                toSetDefault()
            }
        }
    }

    private fun navigateToThemePack() {
        val intent = Intent(this, ThemePackActivity::class.java).apply {
            putExtra(ThemePackActivity.EXTRA_FROM_INTRO, true)
        }
        themePackLauncher.launch(intent)
    }
}

@Composable
private fun Providers(
    content: @Composable () -> Unit,
) {
    ProvideLifecycleState {
        ProvideBottomSheetHandler {
            content()
        }
    }
}
