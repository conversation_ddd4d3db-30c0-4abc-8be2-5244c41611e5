package com.live.wallpapers.widgets.themes.activities.themepack.screens

import android.annotation.SuppressLint
import android.app.Activity
import android.net.Uri
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.PlayerView
import coil3.compose.SubcomposeAsyncImage
import com.android.launcher3.R
import com.live.wallpapers.widgets.themes.analytics.logger
import com.live.wallpapers.widgets.themes.analytics.model.LogEventNames
import com.live.wallpapers.widgets.themes.analytics.model.LogEventParams
import com.live.wallpapers.widgets.themes.analytics.model.ScreenNames
import com.live.wallpapers.widgets.themes.livewallpaper.LiveWallpaperHelper
import com.live.wallpapers.widgets.themes.livewallpaper.VideoWallpaperService
import com.live.wallpapers.widgets.themes.preferences.getAdapter
import com.live.wallpapers.widgets.themes.preferences2.preferenceManager2
import com.live.wallpapers.widgets.themes.ui.bottomsheet.SetLiveWallpaperBottomSheetContent
import com.live.wallpapers.widgets.themes.ui.preferences.LocalNavController
import com.live.wallpapers.widgets.themes.ui.preferences.components.LoadingOverlay
import com.live.wallpapers.widgets.themes.ui.preferences.components.NoInternetConnectionView
import com.live.wallpapers.widgets.themes.ui.util.BottomSheetOptions
import com.live.wallpapers.widgets.themes.ui.util.bottomSheetHandler
import com.live.wallpapers.widgets.themes.util.lifecycleState
import com.live.wallpapers.widgets.themes.util.rememberNetworkState
import java.io.File
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter")
@Composable
fun WallpaperPreviewScreen(
    wallpaperId: Int,
    viewModel: WallpaperPreviewViewModel = viewModel(),
    isFromIntro: Boolean = false,
    onNavigateBackToIntro: () -> Unit = {},
) {
    val context = LocalContext.current
    val navController = LocalNavController.current
    val bottomSheetHandler = bottomSheetHandler
    val coroutineScope = rememberCoroutineScope()
    val prefs = preferenceManager2()
    val homeTextColorAdapter = prefs.homeTextColor.getAdapter()

    val state by viewModel.state.collectAsState()
    val networkState by rememberNetworkState()
    val isConnected by remember(networkState) {
        derivedStateOf {
            networkState.isConnected()
        }
    }
    var previousNetworkState by remember { mutableStateOf(networkState) }

    // State for live wallpaper download
    var isDownloadingWallpaper by remember { mutableStateOf(false) }
    var downloadProgress by remember { mutableStateOf(0) }

    val wallpaper = state.wallpaper
    val isLiveWallpaper = wallpaper?.isLiveWallpaper == true

    val logParams = mapOf(
        LogEventParams.WALLPAPER_ID to wallpaperId,
        LogEventParams.IS_VIDEO to isLiveWallpaper,
    )

    // Auto-refresh data when network connection is restored
    LaunchedEffect(networkState) {
        if (!previousNetworkState.isConnected() && networkState.isConnected()) {
            val currentState = viewModel.state.value
            val shouldRefresh = currentState.error != null || currentState.wallpaper == null

            if (shouldRefresh) {
                viewModel.loadWallpaper(wallpaperId)
            }
        }
        previousNetworkState = networkState
    }

    // Load wallpaper when the screen is first displayed
    LaunchedEffect(wallpaperId) {
        viewModel.logViewScreen(wallpaperId)
        viewModel.loadWallpaper(wallpaperId)
    }

    LaunchedEffect(state.isSuccess) {
        if (state.isSuccess) {
            wallpaper?.textColor?.let { color ->
                homeTextColorAdapter.onChange(color)
            }
            if (isFromIntro) {
                onNavigateBackToIntro()
            } else {
                navController.popBackStack()
            }
        }
    }

    var isLiveWallpaperSet by remember { mutableStateOf(false) }

    val launcher =
        rememberLauncherForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                if (LiveWallpaperHelper.isLiveWallpaperSet(context)) {
                    if (isLiveWallpaperSet) {
                        VideoWallpaperService.sendUpdateVideoIntent(context)
                    }
                    logger().logClickEvent(
                        screenName = ScreenNames.WALLPAPER_PREVIEW,
                        eventName = LogEventNames.BTN_APPLY_WALLPAPER_LIVE_SUCCESS,
                        params = logParams,
                    )
                    Toast.makeText(
                        context,
                        R.string.wallpaper_applied_successfully,
                        Toast.LENGTH_SHORT,
                    ).show()
                    viewModel.setIsSuccess(true)
                } else {
                    Toast.makeText(
                        context,
                        R.string.wallpaper_apply_error,
                        Toast.LENGTH_LONG,
                    ).show()
                    viewModel.setApplying(false)
                }
            } else {
                viewModel.setApplying(false)
            }
        }

    val onApplyWallpaper: () -> Unit = {
        logger().logClickEvent(
            screenName = ScreenNames.WALLPAPER_PREVIEW,
            eventName = if (isLiveWallpaper) LogEventNames.BTN_APPLY_WALLPAPER_LIVE else LogEventNames.BTN_APPLY_WALLPAPER,
            params = logParams,
        )
        if (isLiveWallpaper) {
            logger().logClickEvent(
                screenName = ScreenNames.WALLPAPER_PREVIEW,
                eventName = LogEventNames.VIEW_LIVE_WARNING_POPUP,
                params = logParams,
            )
            bottomSheetHandler.show(
                BottomSheetOptions(
                    showDrag = false,
                    sheetModifier = Modifier
                        .padding(
                            start = 16.dp,
                            end = 16.dp,
                            bottom = 8.dp,
                        )
                        .navigationBarsPadding(),
                ) {
                    SetLiveWallpaperBottomSheetContent(
                        onClose = {
                            logger().logClickEvent(
                                screenName = ScreenNames.WALLPAPER_PREVIEW,
                                eventName = LogEventNames.BTN_LIVE_WARNING_POPUP_CANCEL,
                                params = logParams,
                            )
                            bottomSheetHandler.hide()
                        },
                        onConfirm = {
                            logger().logClickEvent(
                                screenName = ScreenNames.WALLPAPER_PREVIEW,
                                eventName = LogEventNames.BTN_LIVE_WARNING_POPUP_OK,
                                params = logParams,
                            )
                            bottomSheetHandler.hide()

                            isDownloadingWallpaper = true
                            downloadProgress = 0


                            viewModel.downloadLiveWallpaper(
                                onProgress = { progress ->
                                    downloadProgress = progress
                                },
                                onComplete = { success, videoFile, thumbnailFile ->
                                    isDownloadingWallpaper = false

                                    if (success && videoFile != null) {
                                        coroutineScope.launch {
                                            LiveWallpaperHelper.setLiveWallpaper(
                                                context = context,
                                                videoFile = videoFile,
                                                thumbnailFile = thumbnailFile,
                                                screenName = ScreenNames.WALLPAPER_PREVIEW,
                                                onLaunchIntent = { intent ->
                                                    isLiveWallpaperSet =
                                                        LiveWallpaperHelper.isLiveWallpaperSet(
                                                            context,
                                                        )

                                                    if (intent != null) {
                                                        launcher.launch(intent)
                                                    } else {
                                                        Toast.makeText(
                                                            context,
                                                            R.string.error_wallpaper_chooser,
                                                            Toast.LENGTH_LONG,
                                                        ).show()
                                                        viewModel.setApplying(false)
                                                    }
                                                },
                                            )
                                        }
                                    }
                                },
                            )
                        },
                    )
                },
            )
        } else {
            viewModel.applyWallpaper()
        }
    }


    Scaffold(
        containerColor = Color.Transparent,
        contentWindowInsets = WindowInsets(0, 0, 0, 0),
    ) { _ ->
        Box(
            modifier = Modifier.fillMaxSize(),
        ) {
            when {
                !isConnected || state.error != null -> {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .safeDrawingPadding(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        // Back button at top
                        val topAppBarColors = TopAppBarDefaults.topAppBarColors()
                        Row(
                            modifier = Modifier
                                .align(Alignment.Start)
                                .padding(start = 8.dp, top = 8.dp),
                        ) {
                            CompositionLocalProvider(
                                LocalContentColor provides topAppBarColors.navigationIconContentColor,
                                content = {
                                    IconButton(
                                        onClick = {
                                            logger().logClickEvent(
                                                screenName = ScreenNames.WALLPAPER_PREVIEW,
                                                eventName = LogEventNames.BTN_BACK,
                                            )
                                            navController.popBackStack()
                                        },
                                    ) {
                                        Icon(
                                            painter = painterResource(R.drawable.ic_arrow_back),
                                            contentDescription = stringResource(R.string.action_back),
                                            modifier = Modifier.size(24.dp),
                                        )
                                    }
                                },
                            )
                        }

                        // Center content
                        Box(
                            modifier = Modifier.weight(1f),
                            contentAlignment = Alignment.Center,
                        ) {
                            NoInternetConnectionView()
                        }

                        // Retry button at bottom
                        Box(
                            modifier = Modifier
                                .padding(horizontal = 16.dp, vertical = 8.dp),
                        ) {
                            Box(
                                modifier = Modifier
                                    .height(48.dp)
                                    .fillMaxWidth()
                                    .clip(RoundedCornerShape(40.dp))
                                    .background(MaterialTheme.colorScheme.primary)
                                    .clickable(
                                        onClick = {
                                            if (!isConnected) {
                                                Toast.makeText(
                                                    context,
                                                    R.string.no_internet_connection_please_check_your_network,
                                                    Toast.LENGTH_SHORT,
                                                ).show()
                                            } else {
                                                viewModel.resetState()
                                                viewModel.loadWallpaper(wallpaperId)
                                            }
                                        },
                                    )
                                    .padding(vertical = 12.dp, horizontal = 24.dp),
                                contentAlignment = Alignment.Center,
                            ) {
                                Text(
                                    text = stringResource(R.string.action_retry),
                                    style = MaterialTheme.typography.bodyLarge.copy(
                                        fontWeight = FontWeight.W600,
                                        color = MaterialTheme.colorScheme.onPrimary,
                                    ),
                                )
                            }
                        }
                    }
                }

                state.isLoading -> {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.Black),
                    ) {
                        // Back button at top
                        Row(
                            modifier = Modifier
                                .align(Alignment.Start)
                                .padding(start = 8.dp, top = 8.dp),
                        ) {
                            IconButton(
                                onClick = {
                                    logger().logClickEvent(
                                        screenName = ScreenNames.WALLPAPER_PREVIEW,
                                        eventName = LogEventNames.BTN_BACK,
                                    )
                                    navController.popBackStack()
                                },
                                modifier = Modifier
                                    .statusBarsPadding()
                                    .padding(8.dp)
                                    .clip(CircleShape)
                                    .background(Color.Black.copy(alpha = 0.3f)),
                            ) {
                                Icon(
                                    painter = painterResource(R.drawable.ic_arrow_back),
                                    contentDescription = stringResource(R.string.action_back),
                                    tint = Color.White,
                                    modifier = Modifier.size(24.dp),
                                )
                            }
                        }

                        // Loading indicator
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxWidth(),
                            contentAlignment = Alignment.Center,
                        ) {
                            CircularProgressIndicator(
                                color = Color.White,
                            )
                        }
                    }
                }

                wallpaper != null -> {
                    // Hiển thị video nếu có videoUrl, ngược lại hiển thị hình ảnh
                    if (!wallpaper.videoUrl.isNullOrBlank()) {
                        val downloadedFile = remember(wallpaper.videoUrl) {
                            LiveWallpaperHelper.getDownloadedVideoFile(context, wallpaper.videoUrl)
                        }

                        VideoWallpaperPreview(
                            videoPath = downloadedFile?.absolutePath ?: wallpaper.videoUrl,
                            isLocalFile = downloadedFile != null,
                            shouldPause = state.isApplying || isDownloadingWallpaper,
                            modifier = Modifier.fillMaxSize(),
                        )
                    } else {
                        SubcomposeAsyncImage(
                            model = wallpaper.url ?: wallpaper.thumb ?: "",
                            contentDescription = wallpaper.name,
                            contentScale = ContentScale.Crop,
                            modifier = Modifier.fillMaxSize(),
                            loading = {
                                Box(
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .background(Color.Black),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    CircularProgressIndicator(
                                        color = MaterialTheme.colorScheme.primary,
                                    )
                                }
                            },
                            error = {
                                Box(
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .background(Color.Black),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    Icon(
                                        painter = painterResource(R.drawable.load_image_error),
                                        contentDescription = null,
                                        tint = Color.White,
                                        modifier = Modifier.size(88.dp),
                                    )
                                }
                            },
                        )
                    }

//                    // Gradient overlay for better text visibility
//                    Box(
//                        modifier = Modifier
//                            .fillMaxSize()
//                            .background(
//                                brush = Brush.verticalGradient(
//                                    colors = listOf(
//                                        Color.Black.copy(alpha = 0.3f),
//                                        Color.Transparent,
//                                        Color.Transparent,
//                                        Color.Black.copy(alpha = 0.5f),
//                                    ),
//                                ),
//                            ),
//                    )

                    // Back button (top left)
                    Row(
                        modifier = Modifier
                            .align(Alignment.TopStart)
                            .padding(start = 8.dp, top = 8.dp),
                    ) {
                        IconButton(
                            onClick = {
                                logger().logClickEvent(
                                    screenName = ScreenNames.WALLPAPER_PREVIEW,
                                    eventName = LogEventNames.BTN_BACK,
                                )
                                navController.popBackStack()
                            },
                            modifier = Modifier
                                .statusBarsPadding()
                                .padding(8.dp)
                                .clip(CircleShape)
                                .background(Color.Black.copy(alpha = 0.1f)),
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_arrow_back),
                                contentDescription = stringResource(R.string.action_back),
                                tint = Color.White,
                                modifier = Modifier.size(24.dp),
                            )
                        }
                    }

                    val enabled =
                        !state.isApplying && !isDownloadingWallpaper

                    Box(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .navigationBarsPadding()
                            .padding(start = 16.dp, end = 16.dp, bottom = 8.dp),
                    ) {
                        Box(
                            modifier = Modifier
                                .height(48.dp)
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(40.dp))
                                .background(
                                    if (enabled) {
                                        MaterialTheme.colorScheme.primary
                                    } else {
                                        MaterialTheme.colorScheme.surfaceVariant
                                    },
                                )
                                .clickable(
                                    enabled = enabled,
                                    onClick = onApplyWallpaper,
                                )
                                .padding(vertical = 12.dp, horizontal = 24.dp),
                            contentAlignment = Alignment.Center,
                        ) {
                            if (state.isApplying || isDownloadingWallpaper) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    color = Color.Black,
                                    strokeWidth = 2.dp,
                                )
                            } else {
                                Text(
                                    text = stringResource(R.string.action_apply),
                                    style = MaterialTheme.typography.bodyLarge.copy(
                                        fontWeight = FontWeight.W600,
                                        color = MaterialTheme.colorScheme.onPrimary,
                                    ),
                                )
                            }
                        }
                    }
                }
            }
        }

        // Loading overlay for live wallpaper download
        if (isDownloadingWallpaper) {
            LoadingOverlay(
                description = stringResource(R.string.live_wallpaper_download_description),
                loadingProgress = downloadProgress,
            )
        }
    }
}

@androidx.annotation.OptIn(UnstableApi::class)
@Composable
private fun VideoWallpaperPreview(
    videoPath: String,
    modifier: Modifier = Modifier,
    isLocalFile: Boolean = false,
    shouldPause: Boolean = false,
) {
    val context = LocalContext.current
    var isLoading by remember { mutableStateOf(true) }
    val scope = rememberCoroutineScope()

    val exoPlayer = remember {
        ExoPlayer.Builder(context).build().apply {
            repeatMode = Player.REPEAT_MODE_ONE
            volume = 0f
            videoScalingMode = C.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING

            val mediaItem = if (isLocalFile) {
                MediaItem.fromUri(Uri.fromFile(File(videoPath)))
            } else {
                MediaItem.fromUri(videoPath)
            }
            setMediaItem(mediaItem)
            prepare()

            addListener(
                object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        scope.launch {
                            delay(150)
                            isLoading = when (playbackState) {
                                Player.STATE_BUFFERING -> !isLocalFile
                                Player.STATE_READY -> false
                                Player.STATE_ENDED -> false
                                else -> isLoading
                            }
                        }
                    }
                },
            )
        }
    }

    DisposableEffect(exoPlayer) {
        onDispose {
            exoPlayer.release()
        }
    }

    val resumed = lifecycleState().isAtLeast(Lifecycle.State.RESUMED)
    LaunchedEffect(resumed, shouldPause, isLoading) {
        if (resumed && !shouldPause && !isLoading) {
            exoPlayer.play()
        } else {
            exoPlayer.pause()
        }
    }

    Box(modifier = modifier) {
        AndroidView(
            factory = { context ->
                PlayerView(context).apply {
                    useController = false
                    controllerAutoShow = false
                    controllerHideOnTouch = false
                    controllerShowTimeoutMs = 0
                    hideController()
                    player = exoPlayer
                    resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
                }
            },
            modifier = Modifier.fillMaxSize(),
        )

        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black),
                contentAlignment = Alignment.Center,
            ) {
                CircularProgressIndicator(
                    color = MaterialTheme.colorScheme.primary,
                )
            }
        }
    }
}
