package com.live.wallpapers.widgets.themes.analytics.model

/**
 * User property names for Firebase Analytics
 */
object LogUserProperty {
    private const val PREFIX = "up_"

    // Number of times the app has been opened
    const val TOTAL_APP_OPENS = "${PREFIX}total_app_opens"

    // Number of unique days the user has used the app
    const val ACTIVE_DAYS = "${PREFIX}active_days"

    // Whether app is in debug or release mode
    const val BUILD_TYPE = "${PREFIX}build_type"

    // Language of the app
    const val APP_LANGUAGE = "${PREFIX}app_language"
}

/**
 * Event names constants to avoid hardcoded strings
 */
object LogEventNames {
    private const val PREFIX = ""

    // Main events
    const val INIT_FIREBASE_DONE = "${PREFIX}init_firebase_done"
    const val INIT_APP_DONE = "${PREFIX}init_app_done"

    // Remote Config events
    const val INIT_REMOTE_CONFIG_DONE = "${PREFIX}init_remote_config_done"
    const val INIT_REMOTE_CONFIG_ERROR = "${PREFIX}init_remote_config_error"
    const val REMOTE_CONFIG_UPDATED = "${PREFIX}remote_config_updated"
    const val REMOTE_CONFIG_FETCH_ERROR = "${PREFIX}remote_config_fetch_error"

    // Common events
    const val BTN_BACK = "${PREFIX}btn_back"
    const val BTN_SKIP = "${PREFIX}btn_skip"

    // Search function
    const val VIEW_SEARCH = "${PREFIX}view_search"
    const val BTN_SEARCH_TYPING = "${PREFIX}btn_search_typing"
    const val VIEW_SEARCH_RESULT = "${PREFIX}view_search_result"
    const val BTN_SEARCH_RESULT_CLICK = "${PREFIX}btn_search_result_click"
    const val BTN_SHOW_MORE = "${PREFIX}btn_show_more"
    const val BTN_SHOW_LESS = "${PREFIX}btn_show_less"
    const val BTN_RECENT_KEYWORD_CLICK = "${PREFIX}btn_recent_keyword_click"
    const val BTN_CLEAR_RECENT_CLICK = "${PREFIX}btn_clear_recent_click"
    const val VIEW_NOT_FOUND = "${PREFIX}view_not_found"
    const val BTN_DELETE_SEARCH_ITEM = "${PREFIX}btn_delete_search_item"
    const val BTN_APP_RECENT_CLICK = "${PREFIX}btn_app_recent_click"
    const val BTN_CLEAR_ALL_TYPING = "${PREFIX}btn_clear_all_typing"
    const val SWIPE_DOWN_SEARCH_TO_CLOSE = "${PREFIX}swipe_down_search_to_close"
    const val BTN_SETTING_USAGE_ACCESS = "${PREFIX}btn_setting_usage_access"
    const val GRANT_USAGE_ACCESS = "${PREFIX}grant_usage_access"

    // Home Launcher
    const val VIEW_HOME_SCREEN = "${PREFIX}view_home_screen"
    const val BTN_LONG_PRESS_SCREEN = "${PREFIX}btn_long_press_screen"
    const val BTN_LONG_PRESS_SETTING = "${PREFIX}btn_long_press_setting"
    const val BTN_LONG_PRESS_THEME = "${PREFIX}btn_long_press_theme"
    const val BTN_LONG_PRESS_WIDGET = "${PREFIX}btn_long_press_widget"
    const val BTN_SEARCH_DOCK = "${PREFIX}btn_search_dock"
    const val BTN_SEARCH_MIC = "${PREFIX}btn_search_mic"
    const val BTN_HOME_SCROLL = "${PREFIX}btn_home_scroll"
    const val BTN_HOME_APP_CLICK = "${PREFIX}btn_home_app_click"
    const val BTN_HOME_SWIPE_UP = "${PREFIX}btn_home_swipe_up"
    const val BTN_HOME_SWIPE_DOWN = "${PREFIX}btn_home_swipe_down"
    const val BTN_HOME_WIDGET_CLICK = "${PREFIX}btn_home_widget_click"
    const val BTN_HOME_ADD = "${PREFIX}btn_home_add"
    const val BTN_DRAG_DROP = "${PREFIX}btn_drag_drop"
    const val VIEW_POPUP_SET_DEFAULT = "${PREFIX}view_popup_set_default"
    const val BTN_SET_DEFAULT_FROM_HOME = "${PREFIX}btn_set_default_from_home"

    // Drawer
    const val VIEW_APP_DRAWER = "${PREFIX}view_app_drawer"
    const val BTN_APP_DRAWER_CLICK = "${PREFIX}btn_app_drawer_click"
    const val BTN_SEARCH_CLICK = "${PREFIX}btn_search_click"
    const val BTN_APP_LONG_PRESS = "${PREFIX}btn_app_long_press"
    const val BTN_APP_LONG_PRESS_INFO = "${PREFIX}btn_app_long_press_info"
    const val BTN_APP_LONG_PRESS_UNINSTALL = "${PREFIX}btn_app_long_press_uninstall"
    const val BTN_APP_LONG_PRESS_CUSTOMIZE = "${PREFIX}btn_app_long_press_customize"
    const val BTN_SCROLL = "${PREFIX}btn_scroll"
    const val SWIPE_DOWN_DRAWER_TO_CLOSE = "${PREFIX}swipe_down_drawer_to_close"

    // Theme store
    const val VIEW_THEME_STORE = "${PREFIX}view_theme_store"
    const val BTN_CATEGORY_THEME = "${PREFIX}btn_category_theme"
    const val BTN_CATEGORY_THEME_SEE_MORE = "${PREFIX}btn_category_theme_see_more"
    const val BTN_THEME_ITEM = "${PREFIX}btn_theme_item"
    const val BTN_THEME_SECTION_SCROLL = "${PREFIX}btn_theme_section_scroll"
    const val BTN_THEME_CATEGORY_SCROLL = "${PREFIX}btn_theme_category_scroll"

    // Theme category
    const val VIEW_THEME_CATEGORY = "${PREFIX}view_theme_category"
    const val BTN_THEME_LIST_SCROLL = "${PREFIX}btn_theme_list_scroll"
    const val BTN_THEME_LIST_LOAD_MORE = "${PREFIX}btn_theme_list_load_more"

    // Theme preview
    const val VIEW_THEME_PREVIEW = "${PREFIX}view_theme_preview"
    const val BTN_IMAGE_SCROLL = "${PREFIX}btn_image_scroll"
    const val BTN_PLAY_VIDEO = "${PREFIX}btn_play_video"
    const val BTN_PAUSE_VIDEO = "${PREFIX}btn_pause_video"
    const val BTN_APPLY_THEME = "${PREFIX}btn_apply_theme"
    const val BTN_APPLY_THEME_LIVE = "${PREFIX}btn_apply_theme_live"
    const val VIEW_LIVE_WARNING_POPUP = "${PREFIX}view_live_warning_popup"
    const val BTN_LIVE_WARNING_POPUP_OK = "${PREFIX}btn_live_warning_popup_ok"
    const val BTN_LIVE_WARNING_POPUP_CANCEL = "${PREFIX}btn_live_warning_popup_cancel"
    const val BTN_APPLY_THEME_SUCCESS = "${PREFIX}btn_apply_theme_success"
    const val BTN_APPLY_THEME_ERROR = "${PREFIX}btn_apply_theme_error"
    const val BTN_APPLY_THEME_LIVE_SUCCESS = "${PREFIX}btn_apply_theme_live_success"
    const val BTN_APPLY_THEME_LIVE_ERROR = "${PREFIX}btn_apply_theme_live_error"
    const val BTN_DOWNLOAD_ICON_PACK = "${PREFIX}btn_download_icon_pack"
    const val BTN_DOWNLOAD_ICON_PACK_SUCCESS = "${PREFIX}btn_download_icon_pack_success"
    const val BTN_DOWNLOAD_ICON_PACK_ERROR = "${PREFIX}btn_download_icon_pack_error"
    const val BTN_DOWNLOAD_LIVE_WALLPAPER = "${PREFIX}btn_download_live_wallpaper"
    const val BTN_DOWNLOAD_LIVE_WALLPAPER_SUCCESS = "${PREFIX}btn_download_live_wallpaper_success"
    const val BTN_DOWNLOAD_LIVE_WALLPAPER_ERROR = "${PREFIX}btn_download_live_wallpaper_error"
    const val BTN_DOWNLOAD_THUMBNAIL = "${PREFIX}btn_download_thumbnail"
    const val BTN_DOWNLOAD_THUMBNAIL_SUCCESS = "${PREFIX}btn_download_thumbnail_success"
    const val BTN_DOWNLOAD_THUMBNAIL_ERROR = "${PREFIX}btn_download_thumbnail_error"
    const val BTN_DOWNLOAD_WALLPAPER = "${PREFIX}btn_download_wallpaper"
    const val BTN_DOWNLOAD_WALLPAPER_SUCCESS = "${PREFIX}btn_download_wallpaper_success"
    const val BTN_DOWNLOAD_WALLPAPER_ERROR = "${PREFIX}btn_download_wallpaper_error"

    // Wallpaper store
    const val VIEW_WALLPAPER_STORE = "${PREFIX}view_wallpaper_store"
    const val BTN_CATEGORY_WALLPAPER = "${PREFIX}btn_category_wallpaper"
    const val BTN_CATEGORY_WALLPAPER_SEE_MORE = "${PREFIX}btn_category_wallpaper_see_more"
    const val BTN_WALLPAPER_ITEM = "${PREFIX}btn_wallpaper_item"
    const val BTN_WALLPAPER_SECTION_SCROLL = "${PREFIX}btn_wallpaper_section_scroll"
    const val BTN_WALLPAPER_CATEGORY_SCROLL = "${PREFIX}btn_wallpaper_category_scroll"

    // Wallpaper category
    const val VIEW_WALLPAPER_CATEGORY = "${PREFIX}view_wallpaper_category"
    const val BTN_WALLPAPER_LIST_SCROLL = "${PREFIX}btn_wallpaper_list_scroll"
    const val BTN_WALLPAPER_LIST_LOAD_MORE = "${PREFIX}btn_wallpaper_list_load_more"

    // Wallpaper preview
    const val VIEW_WALLPAPER_PREVIEW = "${PREFIX}view_wallpaper_preview"
    const val BTN_APPLY_WALLPAPER = "${PREFIX}btn_apply_wallpaper"
    const val BTN_APPLY_WALLPAPER_LIVE = "${PREFIX}btn_apply_wallpaper_live"
    const val BTN_APPLY_WALLPAPER_SUCCESS = "${PREFIX}btn_apply_wallpaper_success"
    const val BTN_APPLY_WALLPAPER_ERROR = "${PREFIX}btn_apply_wallpaper_error"
    const val BTN_APPLY_WALLPAPER_LIVE_SUCCESS = "${PREFIX}btn_apply_wallpaper_live_success"

    // Icon pack store
    const val VIEW_ICON_PACK_STORE = "${PREFIX}view_icon_pack_store"
    const val BTN_CATEGORY_ICON_PACK = "${PREFIX}btn_category_icon_pack"
    const val BTN_ICON_PACK_ITEM = "${PREFIX}btn_icon_pack_item"
    const val BTN_ICON_PACK_CATEGORY_SCROLL = "${PREFIX}btn_icon_pack_category_scroll"
    const val BTN_ICON_PACK_LIST_SCROLL = "${PREFIX}btn_icon_pack_list_scroll"
    const val BTN_ICON_PACK_LIST_LOAD_MORE = "${PREFIX}btn_icon_pack_list_load_more"

    // Icon pack preview
    const val VIEW_ICON_PACK_PREVIEW = "${PREFIX}view_icon_pack_preview"
    const val BTN_APPLY_ICON_PACK = "${PREFIX}btn_apply_icon_pack"
    const val BTN_APPLY_ICON_PACK_SUCCESS = "${PREFIX}btn_apply_icon_pack_success"
    const val BTN_APPLY_ICON_PACK_ERROR = "${PREFIX}btn_apply_icon_pack_error"

    // Splash
    const val VIEW_SPLASH = "${PREFIX}view_splash"

    // Intro theme
    const val VIEW_INTRO_THEME = "${PREFIX}view_intro_theme"
    const val SCROLL_THEME = "${PREFIX}scroll_theme"
    const val SCROLL_DISCOVER = "${PREFIX}scroll_discover"
    const val BTN_DISCOVER_THEME = "${PREFIX}btn_discover_theme"
    const val LOADING_THEME = "${PREFIX}loading_theme"
    const val LOADING_THEME_SUCCESS = "${PREFIX}loading_theme_success"
    const val LOADING_THEME_ERROR = "${PREFIX}loading_theme_error"
    const val LOADING_DATA = "${PREFIX}loading_data"
    const val LOADING_DATA_SUCCESS = "${PREFIX}loading_data_success"
    const val LOADING_DATA_ERROR = "${PREFIX}loading_data_error"

    // Set default
    const val VIEW_SET_DEFAULT = "${PREFIX}view_set_default"
    const val BTN_SET_DEFAULT = "${PREFIX}btn_set_default"
    const val SET_DEFAULT_SUCCESS = "${PREFIX}set_default_success"

    // Language
    const val VIEW_LANGUAGE = "${PREFIX}view_language"

    // Setting
    const val VIEW_SETTING = "${PREFIX}view_setting"
    const val BTN_SETTING_HOME_SCREEN = "${PREFIX}btn_setting_home_screen"
    const val BTN_SETTING_APP_DRAWER = "${PREFIX}btn_setting_app_drawer"
    const val BTN_SETTING_GESTURE = "${PREFIX}btn_setting_gesture"
    const val BTN_SETTING_DOCK_SCREEN = "${PREFIX}btn_setting_dock_screen"
    const val BTN_SETTING_APPEARANCE = "${PREFIX}btn_setting_appearance"
    const val BTN_SETTING_GENERAL = "${PREFIX}btn_setting_general"
    const val BTN_SETTING_PRIVACY_POLICY = "${PREFIX}btn_setting_privacy_policy"
    const val BTN_SETTING_LANGUAGE = "${PREFIX}btn_setting_language"

    // Setting home screen
    const val VIEW_SETTING_HOME_SCREEN = "${PREFIX}view_setting_home_screen"
    const val BTN_TOGGLE_LOCK_LAYOUT = "${PREFIX}btn_toggle_lock_layout"
    const val BTN_TOGGLE_ADD_NEW_APP = "${PREFIX}btn_toggle_add_new_app"
    const val BTN_GRID_SIZE = "${PREFIX}btn_grid_size"
    const val BTN_TOGGLE_SHOW_LABEL = "${PREFIX}btn_toggle_show_label"
    const val BTN_RESIZE_ICON_SIZE = "${PREFIX}btn_resize_icon_size"

    // Setting app drawer
    const val VIEW_SETTING_APP_DRAWER = "${PREFIX}view_setting_app_drawer"
    const val BTN_TOGGLE_SHOW_SCROLL_BAR = "${PREFIX}btn_toggle_show_scroll_bar"
    const val BTN_APP_DRAWER_COLUMN = "${PREFIX}btn_app_drawer_column"

    // Setting dock screen
    const val VIEW_SETTING_DOCK_SCREEN = "${PREFIX}view_setting_dock_screen"
    const val BTN_TOGGLE_SHOW_DOCK = "${PREFIX}btn_toggle_show_dock"
    const val BTN_ICON_COUNT = "${PREFIX}btn_icon_count"
    const val BTN_SEARCH_PROVIDER = "${PREFIX}btn_search_provider"
    const val BTN_SEARCH_PROVIDER_ITEM = "${PREFIX}btn_search_provider_item"
    const val BTN_RESIZE_BACKGROUND_OPACITY = "${PREFIX}btn_resize_background_opacity"

    // Setting appearance
    const val VIEW_SETTING_APPEARANCE = "${PREFIX}view_setting_appearance"
    const val BTN_LIGHT_MODE = "${PREFIX}btn_light_mode"
    const val BTN_DARK_MODE = "${PREFIX}btn_dark_mode"
    const val BTN_SYSTEM_MODE = "${PREFIX}btn_system_mode"
    const val BTN_THEME_PACK = "${PREFIX}btn_theme_pack"
    const val BTN_ICON_PACK = "${PREFIX}btn_icon_pack"
    const val BTN_WALLPAPER_PACK = "${PREFIX}btn_wallpaper_pack"

    // Setting general
    const val VIEW_SETTING_GENERAL = "${PREFIX}view_setting_general"
    const val BTN_RESET_DEFAULT_LAYOUT = "${PREFIX}btn_reset_default_layout"
    const val BTN_RESET_DEFAULT_LAYOUT_OK = "${PREFIX}btn_reset_default_layout_ok"
    const val BTN_RESET_DEFAULT_LAYOUT_CANCEL = "${PREFIX}btn_reset_default_layout_cancel"
    const val BTN_HIDDEN_APP = "${PREFIX}btn_hidden_app"
    const val BTN_NOTIFICATION_DOTS = "${PREFIX}btn_notification_dots"
    const val BTN_NOTIFICATION_DOTS_CANCEL = "${PREFIX}btn_notification_dots_cancel"
    const val BTN_NOTIFICATION_DOTS_OPEN_SETTINGS = "${PREFIX}btn_notification_dots_open_settings"
    const val BTN_TOGGLE_SHOW_NOTIFICATION_COUNT = "${PREFIX}btn_toggle_show_notification_count"

    // Hidden app
    const val VIEW_HIDDEN_APP = "${PREFIX}view_hidden_app"
    const val BTN_TOGGLE_HIDDEN_APP_ITEM = "${PREFIX}btn_toggle_hidden_app_item"

    // Setting gesture
    const val VIEW_SETTING_GESTURE = "${PREFIX}view_setting_gesture"
    const val BTN_SWIPE_UP = "${PREFIX}btn_swipe_up"
    const val BTN_SWIPE_UP_SELECT = "${PREFIX}btn_swipe_up_select"
    const val BTN_SWIPE_DOWN = "${PREFIX}btn_swipe_down"
    const val BTN_SWIPE_DOWN_SELECT = "${PREFIX}btn_swipe_down_select"

    // Start page
    const val VIEW_START_PAGE = "${PREFIX}view_start_page"

    // Live wallpaper
    const val BTN_LIVE_WALLPAPER_INTENT = "${PREFIX}btn_live_wallpaper_intent"
    const val BTN_LIVE_WALLPAPER_CHOOSER_INTENT = "${PREFIX}btn_live_wallpaper_chooser_intent"
    const val BTN_LIVE_WALLPAPER_CHOOSER_ERROR = "${PREFIX}btn_live_wallpaper_chooser_error"
}

/**
 * Parameter keys constants to avoid hardcoded strings
 */
object LogEventParams {
    private const val PREFIX = "ep_"

    // Common params
    const val DATETIME = "${PREFIX}datetime"
    const val TIME_FROM_INIT = "${PREFIX}time_from_init"
    const val CURRENT_SCREEN = "${PREFIX}current_screen"
    const val CLASS_NAME = "${PREFIX}class_name"
    const val EVENT_TYPE = "${PREFIX}event_type"
    const val CATEGORY_ID = "${PREFIX}category_id"
    const val THEME_ID = "${PREFIX}theme_id"
    const val WALLPAPER_ID = "${PREFIX}wallpaper_id"
    const val ICON_PACK_ID = "${PREFIX}icon_pack_id"
    const val IS_VIDEO = "${PREFIX}is_video"
    const val TIME_DOWNLOAD_ICON_PACK = "${PREFIX}time_download_icon_pack"
    const val TIME_DOWNLOAD_LIVE_WALLPAPER = "${PREFIX}time_download_live_wallpaper"
    const val TIME_DOWNLOAD_THUMBNAIL = "${PREFIX}time_download_thumbnail"
    const val TIME_DOWNLOAD_WALLPAPER = "${PREFIX}time_download_wallpaper"
    const val IS_ENABLED = "${PREFIX}is_enabled"
    const val ICON_SIZE_FACTOR = "${PREFIX}icon_size_factor"
    const val GRID_SIZE = "${PREFIX}grid_size"
    const val COLUMN_COUNT = "${PREFIX}column_count"
    const val SEARCH_PROVIDER = "${PREFIX}search_provider"
    const val BACKGROUND_OPACITY = "${PREFIX}background_opacity"
    const val APP_NAME = "${PREFIX}app_name"
    const val IS_HIDDEN = "${PREFIX}is_hidden"
    const val GESTURE_HANDLER = "${PREFIX}gesture_handler"
    const val PACKAGE_NAME = "${PREFIX}package_name"
    const val DEVICE_MODEL = "${PREFIX}device_model"
    const val DEVICE_MANUFACTURER = "${PREFIX}device_manufacturer"
    const val DEVICE_BRAND = "${PREFIX}device_brand"
    const val ANDROID_VERSION = "${PREFIX}android_version"
}
