package com.live.wallpapers.widgets.themes.ui.preferences

import android.content.Intent
import android.view.LayoutInflater
import androidx.activity.enableEdgeToEdge
import com.android.launcher3.R
import com.hk.base.ads.provider.common.hideNavigationBar
import com.hk.base.ads.provider.splash.BaseSplashActivity
import com.live.wallpapers.widgets.themes.analytics.logger
import com.live.wallpapers.widgets.themes.analytics.model.ScreenNames
import com.live.wallpapers.widgets.themes.config.AppRemoteConfig
import com.live.wallpapers.widgets.themes.config.RemoteConfigKeys

class LauncherSettingSplash : BaseSplashActivity() {

    override fun initView() {
        enableEdgeToEdge()
        val layoutInflater = LayoutInflater.from(this)
        val rootView = layoutInflater.inflate(R.layout.activity_theme_splash, null)
        setContentView(rootView)
        hideNavigationBar()
    }

    override fun showStartPage(): Boolean {
        return false
    }

    override fun startApp() {
        startActivity(Intent(this, LauncherSettingActivity::class.java))
        finish()
    }

    override fun startStartPage() {
    }

    override fun splashAdsType(): Int {
        val showAd = AppRemoteConfig.getInstance()
            .getBoolean(RemoteConfigKeys.Ads.ENABLE_INTERSTITIAL_SPLASH_SETTING, true)
        return if (showAd) SPLASH_TYPE_INTER else SPLASH_TYPE_NONE
    }

    override fun onResume() {
        super.onResume()
        logger().setCurrentScreenName(ScreenNames.SETTINGS_SPLASH)
    }
}
