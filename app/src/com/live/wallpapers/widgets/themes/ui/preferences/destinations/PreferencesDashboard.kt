package com.live.wallpapers.widgets.themes.ui.preferences.destinations

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.LauncherApps
import android.os.Build
import android.os.Process
import android.provider.Settings
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Build
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.surfaceColorAtElevation
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.getSystemService
import androidx.core.net.toUri
import com.android.launcher3.BuildConfig
import com.android.launcher3.R
import com.hk.base.ads.provider.common.getCurrentLanguage
import com.hk.base.ads.provider.common.setCurrentLanguage
import com.hk.base.ads.provider.nativead.NativeType
import com.live.wallpapers.widgets.themes.AILauncher
import com.live.wallpapers.widgets.themes.MyApplication
import com.live.wallpapers.widgets.themes.activities.language.LanguageActivity
import com.live.wallpapers.widgets.themes.analytics.logger
import com.live.wallpapers.widgets.themes.analytics.model.LogEventNames
import com.live.wallpapers.widgets.themes.analytics.model.ScreenNames
import com.live.wallpapers.widgets.themes.config.AppRemoteConfig
import com.live.wallpapers.widgets.themes.config.LauncherConfig
import com.live.wallpapers.widgets.themes.config.RemoteConfigKeys
import com.live.wallpapers.widgets.themes.preferences.observeAsState
import com.live.wallpapers.widgets.themes.preferences.preferenceManager
import com.live.wallpapers.widgets.themes.ui.preferences.components.controls.PreferenceCategory
import com.live.wallpapers.widgets.themes.ui.preferences.components.layout.ClickableIcon
import com.live.wallpapers.widgets.themes.ui.preferences.components.layout.PreferenceTemplate
import com.live.wallpapers.widgets.themes.ui.preferences.navigation.Routes
import com.live.wallpapers.widgets.themes.ui.util.addIf
import com.live.wallpapers.widgets.themes.util.isDefaultLauncher

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PreferencesDashboard(
    currentRoute: String,
    onNavigate: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val enableDebug by preferenceManager().enableDebugMenu.observeAsState()
    val highlightColor = MaterialTheme.colorScheme.surfaceColorAtElevation(4.dp)
    val highlightShape = MaterialTheme.shapes.large

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = stringResource(R.string.derived_app_name),
                        style = MaterialTheme.typography.displayMedium.copy(
                            brush = Brush.horizontalGradient(
                                colors = listOf(
                                    Color(0xFF6275FF),
                                    Color(0xFF3CFF52),
                                ),
                            ),
                        ),
                    )
                },
                actions = {
                    if (enableDebug) {
                        val resolvedRoute = Routes.DEBUG_MENU
                        ClickableIcon(
                            imageVector = Icons.Rounded.Build,
                            onClick = { onNavigate(resolvedRoute) },
                            modifier = Modifier.addIf(currentRoute.contains(resolvedRoute)) {
                                Modifier
                                    .clip(highlightShape)
                                    .background(highlightColor)
                            },
                        )
                    }
                },
            )
        },
    ) {
        Column(
            modifier = modifier
                .padding(it)
                .fillMaxSize(),
        ) {
            Column(
                modifier = Modifier
                    .weight(1f)
                    .verticalScroll(rememberScrollState()),
            ) {
                if (!context.isDefaultLauncher()) {
                    PreferencesSetDefaultLauncherWarning()
                    Spacer(modifier = Modifier.height(12.dp))
                }

                BannerCarousel(
                    modifier = Modifier.padding(horizontal = 16.dp),
                )

                HorizontalDivider(
                    modifier = Modifier.padding(
                        start = 16.dp,
                        top = 24.dp,
                        end = 16.dp,
                        bottom = 16.dp,
                    ),
                    color = Color(0xFFE2E2E9),
                )

                Text(
                    stringResource(R.string.settings),
                    style = MaterialTheme.typography.headlineLarge,
                    modifier = Modifier.padding(horizontal = 16.dp),
                )

                Spacer(modifier = Modifier.height(16.dp))

                PreferenceCategory(
                    label = stringResource(R.string.home_screen_label),
                    description = stringResource(R.string.home_screen_description),
                    iconResource = R.drawable.ic_home_setting,
                    onNavigate = {
                        logger().logClickEvent(
                            screenName = ScreenNames.SETTINGS,
                            eventName = LogEventNames.BTN_SETTING_HOME_SCREEN,
                        )
                        val activity = context as? Activity
                        activity?.let { act ->
                            MyApplication.getInstance().showInter(
                                act,
                                RemoteConfigKeys.Ads.ENABLE_INTERSTITIAL_SETTING_HOME,
                            ) {
                                onNavigate(Routes.HOME_SCREEN)
                            }
                        } ?: run {
                            // Fallback if activity is null
                            onNavigate(Routes.HOME_SCREEN)
                        }
                    },
                    isSelected = currentRoute.contains(Routes.HOME_SCREEN),
                )

                Spacer(modifier = Modifier.height(8.dp))

                PreferenceCategory(
                    label = stringResource(R.string.app_drawer_label),
                    description = stringResource(R.string.app_drawer_description),
                    iconResource = R.drawable.ic_app_drawer,
                    onNavigate = {
                        logger().logClickEvent(
                            screenName = ScreenNames.SETTINGS,
                            eventName = LogEventNames.BTN_SETTING_APP_DRAWER,
                        )
                        val activity = context as? Activity
                        activity?.let { act ->
                            MyApplication.getInstance().showInter(
                                act,
                                RemoteConfigKeys.Ads.ENABLE_INTERSTITIAL_SETTING_APP_DRAWER,
                            ) {
                                onNavigate(Routes.APP_DRAWER)
                            }
                        } ?: run {
                            // Fallback if activity is null
                            onNavigate(Routes.APP_DRAWER)
                        }
                    },
                    isSelected = currentRoute.contains(Routes.APP_DRAWER),
                )

                Spacer(modifier = Modifier.height(8.dp))

                PreferenceCategory(
                    label = stringResource(id = R.string.gestures_label),
                    description = stringResource(R.string.gestures_description),
                    iconResource = R.drawable.ic_gesture_setting,
                    onNavigate = {
                        logger().logClickEvent(
                            screenName = ScreenNames.SETTINGS,
                            eventName = LogEventNames.BTN_SETTING_GESTURE,
                        )
                        val activity = context as? Activity
                        activity?.let { act ->
                            MyApplication.getInstance().showInter(
                                act,
                                RemoteConfigKeys.Ads.ENABLE_INTERSTITIAL_SETTING_GESTURES,
                            ) {
                                onNavigate(Routes.GESTURES)
                            }
                        } ?: run {
                            // Fallback if activity is null
                            onNavigate(Routes.GESTURES)
                        }
                    },
                    isSelected = currentRoute.contains(Routes.GESTURES),
                )

                Spacer(modifier = Modifier.height(8.dp))

                PreferenceCategory(
                    label = stringResource(R.string.dock_label),
                    description = stringResource(R.string.dock_description),
                    iconResource = R.drawable.ic_folder_setting,
                    onNavigate = {
                        logger().logClickEvent(
                            screenName = ScreenNames.SETTINGS,
                            eventName = LogEventNames.BTN_SETTING_DOCK_SCREEN,
                        )
                        val activity = context as? Activity
                        activity?.let { act ->
                            MyApplication.getInstance().showInter(
                                act,
                                RemoteConfigKeys.Ads.ENABLE_INTERSTITIAL_SETTING_DOCK,
                            ) {
                                onNavigate(Routes.DOCK)
                            }
                        } ?: run {
                            // Fallback if activity is null
                            onNavigate(Routes.DOCK)
                        }
                    },
                    isSelected = currentRoute.contains(Routes.DOCK),
                )

                Spacer(modifier = Modifier.height(8.dp))

//            PreferenceCategory(
//                label = stringResource(R.string.folders_label),
//                description = stringResource(R.string.folders_description),
//                iconResource = R.drawable.ic_folder_setting,
//                onNavigate = { onNavigate(Routes.FOLDERS) },
//                isSelected = currentRoute.contains(Routes.FOLDERS),
//            )
//
//            Spacer(modifier = Modifier.height(8.dp))

                PreferenceCategory(
                    label = stringResource(R.string.appearance_label),
                    description = stringResource(R.string.appearance_description),
                    iconResource = R.drawable.ic_appearance_setting,
                    onNavigate = {
                        logger().logClickEvent(
                            screenName = ScreenNames.SETTINGS,
                            eventName = LogEventNames.BTN_SETTING_APPEARANCE,
                        )
                        val activity = context as? Activity
                        activity?.let { act ->
                            MyApplication.getInstance().showInter(
                                act,
                                RemoteConfigKeys.Ads.ENABLE_INTERSTITIAL_SETTING_APPEARANCE,
                            ) {
                                onNavigate(Routes.APPEARANCE)
                            }
                        } ?: run {
                            // Fallback if activity is null
                            onNavigate(Routes.APPEARANCE)
                        }
                    },
                    isSelected = currentRoute.contains(Routes.APPEARANCE),
                )

                Spacer(modifier = Modifier.height(8.dp))

                PreferenceCategory(
                    label = stringResource(R.string.general_label),
                    description = stringResource(R.string.general_description),
                    iconResource = R.drawable.ic_general_setting,
                    onNavigate = {
                        logger().logClickEvent(
                            screenName = ScreenNames.SETTINGS,
                            eventName = LogEventNames.BTN_SETTING_GENERAL,
                        )
                        val activity = context as? Activity
                        activity?.let { act ->
                            MyApplication.getInstance().showInter(
                                act,
                                RemoteConfigKeys.Ads.ENABLE_INTERSTITIAL_SETTING_GENERAL,
                            ) {
                                onNavigate(Routes.GENERAL)
                            }
                        } ?: run {
                            // Fallback if activity is null
                            onNavigate(Routes.GENERAL)
                        }
                    },
                    isSelected = currentRoute.contains(Routes.GENERAL),
                )

                Spacer(modifier = Modifier.height(8.dp))

                val privacyPolicyIntent =
                    Intent(Intent.ACTION_VIEW, LauncherConfig.Config.POLICY.toUri())
                PreferenceCategory(
                    label = stringResource(R.string.privacy_policy_label),
                    description = stringResource(R.string.privacy_policy_description),
                    iconResource = R.drawable.ic_policy,
                    onNavigate = {
                        logger().logClickEvent(
                            screenName = ScreenNames.SETTINGS,
                            eventName = LogEventNames.BTN_SETTING_PRIVACY_POLICY,
                        )
                        context.startActivity(privacyPolicyIntent)
                    },
                )

                Spacer(modifier = Modifier.height(8.dp))

                val currentLang = rememberCurrentAppLanguage()

                PreferenceCategory(
                    label = stringResource(R.string.language_label),
                    description = currentLang.languageText,
                    iconResource = R.drawable.ic_language,
                    onNavigate = {
                        logger().logClickEvent(
                            screenName = ScreenNames.SETTINGS,
                            eventName = LogEventNames.BTN_SETTING_LANGUAGE,
                        )
                        Intent(context, LanguageActivity::class.java)
                            .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                            .also {
                                context.startActivity(it)
                            }
                    },
                )

                Spacer(modifier = Modifier.height(24.dp))
            }

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    stringResource(R.string.version_format, "1.0.0"),
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.W500,
                        color = Color(0xFFCCCCCC),
                    ),
                )
            }
            val shouldShowNative =
                AppRemoteConfig.getInstance().getBoolean(
                    RemoteConfigKeys.Ads.ENABLE_NATIVE_HOME_SETTING,
                    defaultValue = true,
                )
            if (shouldShowNative) {
                NativeAdBlock(
                    screen = ScreenNames.SETTINGS,
                    nativeType = NativeType.MEDIUM,
                    adId = BuildConfig.ADMOB_NATIVE_HOME_SETTING,
                    layoutId = R.id.native_ad_container_in_home_setting,
                    modifier = Modifier.padding(vertical = 8.dp, horizontal = 16.dp),
                )
            }
        }
    }
}

val String.languageText: String
    get() = when (this) {
        "en" -> "English"
        "vi" -> "Tiếng Việt"
        "es" -> "Español"
        "de" -> "Deutsch"
        "pt" -> "Português"
        "in" -> "Indonesia"
        "hi" -> "हिन्दी"
        "ko" -> "한국어"
        else -> this
    }

data class LanguageResult(
    val language: String,
    val shouldSetLanguage: Boolean,
)

fun getCurrentAppLanguage(context: Context): LanguageResult {
    if (context is Activity) {
        val currentLanguage = context.getCurrentLanguage(useDefault = false)
        val defaultLanguage = context.getCurrentLanguage()

        return if (currentLanguage.language.isBlank()) {
            LanguageResult(defaultLanguage.language, shouldSetLanguage = true)
        } else {
            LanguageResult(currentLanguage.language, shouldSetLanguage = false)
        }
    }

    val language = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
        context.resources.configuration.locales.get(0).language
    } else {
        context.resources.configuration.locale.language
    }
    return LanguageResult(language, shouldSetLanguage = false)
}

@Composable
fun rememberCurrentAppLanguage(): String {
    val context = LocalContext.current
    val result = remember(context) {
        getCurrentAppLanguage(context)
    }

    LaunchedEffect(result.shouldSetLanguage) {
        if (result.shouldSetLanguage && context is Activity) {
            context.setCurrentLanguage(context.getCurrentLanguage())
        }
    }

    return result.language
}

@Composable
private fun BannerCarousel(
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    Box(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(min = 96.dp)
            .clip(RoundedCornerShape(24.dp)),
    ) {
        Image(
            painterResource(R.drawable.bg_settings_banner),
            contentDescription = "Banner",
            contentScale = ContentScale.Crop,
            modifier = Modifier.matchParentSize(),
        )
        Row(
            modifier = Modifier
                .align(Alignment.Center)
                .fillMaxWidth()
                .padding(16.dp),
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    stringResource(R.string.banner_title),
                    fontSize = 24.sp,
                    lineHeight = 23.5.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                )
                Text(
                    stringResource(R.string.banner_subtitle),
                    fontSize = 14.sp,
                    lineHeight = 18.5.sp,
                    fontWeight = FontWeight.W400,
                    color = Color.White,
                )
            }
            Spacer(modifier = Modifier.width(10.dp))
//            Image(
//                painterResource(R.drawable.ic_pro_banner),
//                contentDescription = "Icon Pro",
//                contentScale = ContentScale.Crop,
//                modifier = Modifier.width(48.dp),
//            )
        }
    }
}

@Composable
fun PreferencesSetDefaultLauncherWarning(
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    Surface(
        modifier = modifier
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(24.dp),
        color = Color(0xFFE7F0FE),
    ) {
        PreferenceTemplate(
            verticalPadding = 12.dp,
            modifier = Modifier
                .clickable {
                    Intent(Settings.ACTION_HOME_SETTINGS)
                        .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        .let { context.startActivity(it) }
                    (context as? Activity)?.finish()
                },
            contentModifier = Modifier.padding(start = 4.dp),
            title = {},
            description = {
                Text(
                    text = stringResource(
                        id = R.string.set_default_launcher_tip,
                        stringResource(R.string.derived_app_name),
                    ),
                    color = Color(0xFF0D68F1),
                    fontSize = 14.sp,
                    lineHeight = 20.sp,
                    fontWeight = FontWeight.W500,
                )
            },
            startWidget = {
                Box(modifier = Modifier.padding(start = 4.dp)) {
                    Image(
                        painterResource(R.drawable.ic_set_default_setting),
                        contentDescription = "set default",
                        modifier = Modifier.size(34.dp),
                    )
                }
            },
            endWidget = {
                Image(
                    painter = painterResource(id = R.drawable.ic_chevron_right),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    colorFilter = ColorFilter.tint(
                        Color(0xFF0D68F1),
                        blendMode = BlendMode.SrcIn,
                    ),
                )
            },
        )
    }
}

fun openAppInfo(context: Context) {
    val launcherApps = context.getSystemService<LauncherApps>()
    val componentName = ComponentName(context, AILauncher::class.java)
    launcherApps?.startAppDetailsActivity(componentName, Process.myUserHandle(), null, null)
}
