package com.live.wallpapers.widgets.themes.ui.preferences.destinations

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.datastore.preferences.core.Preferences
import com.android.launcher3.R
import com.live.wallpapers.widgets.themes.analytics.logger
import com.live.wallpapers.widgets.themes.analytics.model.LogEventNames
import com.live.wallpapers.widgets.themes.analytics.model.ScreenNames
import com.live.wallpapers.widgets.themes.preferences.PreferenceManager
import com.live.wallpapers.widgets.themes.preferences.getAdapter
import com.live.wallpapers.widgets.themes.preferences.preferenceManager
import com.live.wallpapers.widgets.themes.preferences2.PreferenceManager2
import com.live.wallpapers.widgets.themes.preferences2.preferenceManager2
import com.live.wallpapers.widgets.themes.ui.preferences.LocalIsExpandedScreen
import com.live.wallpapers.widgets.themes.ui.preferences.LocalNavController
import com.live.wallpapers.widgets.themes.ui.preferences.components.controls.ClickablePreference
import com.live.wallpapers.widgets.themes.ui.preferences.components.controls.MainSwitchPreference
import com.live.wallpapers.widgets.themes.ui.preferences.components.controls.SwitchPreference
import com.live.wallpapers.widgets.themes.ui.preferences.components.controls.TextPreference
import com.live.wallpapers.widgets.themes.ui.preferences.components.layout.PreferenceGroup
import com.live.wallpapers.widgets.themes.ui.preferences.components.layout.PreferenceLayout
import com.patrykmichalik.opto.domain.Preference

/**
 * A screen to house unfinished preferences and debug flags
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DebugMenuPreferences(
    modifier: Modifier = Modifier,
) {
    val navController = LocalNavController.current
    val prefs = preferenceManager()
    val prefs2 = preferenceManager2()
    val flags = remember { prefs.debugFlags }
    val flags2 = remember { prefs2.debugFlags }
    val textFlags = remember { prefs2.textFlags }

    val enableDebug = prefs.enableDebugMenu.getAdapter()

    Scaffold(
        modifier = modifier.fillMaxSize(),
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Debug menu",
                        style = MaterialTheme.typography.headlineLarge,
                    )
                },
                navigationIcon = {
                    IconButton(
                        onClick = {
                            logger().logClickEvent(
                                screenName = ScreenNames.SETTING_HOME_SCREEN,
                                eventName = LogEventNames.BTN_BACK,
                            )
                            navController.popBackStack()
                        },
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.ic_arrow_back),
                            contentDescription = stringResource(R.string.action_back),
                            modifier = Modifier.size(24.dp),
                        )
                    }
                },
            )
        },
    ) { innerPaddings ->
        Column(
            modifier = Modifier.padding(innerPaddings)
        ) {
            MainSwitchPreference(
                adapter = enableDebug,
                label = "Show debug menu",
            ) {
                PreferenceGroup {
                    ClickablePreference(
                        label = "Crash launcher",
                        onClick = { throw RuntimeException("User triggered crash") },
                    )
                }

                PreferenceGroup(heading = "Debug flags") {
                    flags2.forEach {
                        SwitchPreference(
                            adapter = it.getAdapter(),
                            label = it.key.name,
                        )
                    }
                    flags.forEach {
                        SwitchPreference(
                            adapter = it.getAdapter(),
                            label = it.key,
                        )
                    }
                    textFlags.forEach {
                        TextPreference(
                            adapter = it.getAdapter(),
                            label = it.key.name,
                        )
                    }
                }
            }
        }
    }
}

private val PreferenceManager2.debugFlags: List<Preference<Boolean, Boolean, Preferences.Key<Boolean>>>
    get() = listOf(showComponentNames)

private val PreferenceManager2.textFlags: List<Preference<String, String, Preferences.Key<String>>>
    get() = listOf(additionalFonts)

private val PreferenceManager.debugFlags
    get() = listOf(ignoreFeedWhitelist)
