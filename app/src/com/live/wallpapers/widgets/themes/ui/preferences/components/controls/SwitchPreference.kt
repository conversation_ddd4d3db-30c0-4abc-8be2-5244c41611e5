package com.live.wallpapers.widgets.themes.ui.preferences.components.controls

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.live.wallpapers.widgets.themes.preferences.PreferenceAdapter
import com.live.wallpapers.widgets.themes.ui.preferences.components.CheckmarkSwitch
import com.live.wallpapers.widgets.themes.ui.preferences.components.layout.PreferenceTemplate
import com.live.wallpapers.widgets.themes.ui.theme.AppTheme
import com.live.wallpapers.widgets.themes.ui.theme.dividerColor
import com.live.wallpapers.widgets.themes.ui.util.AILauncherPreview
import kotlinx.coroutines.delay

@Composable
fun SwitchPreference(
    adapter: PreferenceAdapter<Boolean>,
    label: String,
    modifier: Modifier = Modifier,
    description: String? = null,
    enabled: Boolean = true,
    onClick: (() -> Unit)? = null,
    @DrawableRes startIconRes: Int? = null,
    delayOnChange: Boolean = false,
    delayMillis: Long = 100L,
) {
    val adapterState = adapter.state.value
    var localState by remember(adapterState) { mutableStateOf(adapterState) }
    var pendingChange by remember { mutableStateOf<Boolean?>(null) }

    // Handle delayed state changes
    LaunchedEffect(pendingChange) {
        pendingChange?.let { newValue ->
            delay(delayMillis)
            adapter.onChange(newValue)
            pendingChange = null
        }
    }

    val onCheckedChange: (Boolean) -> Unit = if (delayOnChange) {
        { newValue ->
            localState = newValue
            pendingChange = newValue
        }
    } else {
        adapter::onChange
    }

    val displayValue = if (delayOnChange) localState else adapterState

    SwitchPreference(
        checked = displayValue,
        onCheckedChange = onCheckedChange,
        label = label,
        modifier = modifier,
        description = description,
        onClick = onClick,
        enabled = enabled,
        startIconRes = startIconRes,
    )
}

/**
 * A Preference that provides a two-state toggleable option.
 */
@Composable
fun SwitchPreference(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    description: String? = null,
    enabled: Boolean = true,
    onClick: (() -> Unit)? = null,
    @DrawableRes startIconRes: Int? = null,
) {
    val interactionSource = remember { MutableInteractionSource() }
    val textColor = MaterialTheme.colorScheme.onBackground
    android.util.Log.d("truonghehe", "checked: $checked")
    PreferenceTemplate(
        verticalPadding = 4.dp,
        horizontalPadding = 16.dp,
        startWidget = startIconRes?.let {
            {
                Box(
                    modifier = Modifier.padding(end = 4.dp),
                ) {
                    Icon(
                        painter = painterResource(id = startIconRes),
                        contentDescription = null,
                        tint = textColor,
                        modifier = Modifier.size(24.dp),
                    )
                }

            }

        },
        modifier = modifier
            .clickable(
                enabled = enabled,
                indication = ripple(),
                interactionSource = interactionSource,
            ) {
                if (onClick != null) {
                    onClick()
                } else {
                    onCheckedChange(!checked)
                }
            },
        title = {
            Text(
                text = label,
                style = MaterialTheme.typography.titleMedium,
                color = textColor,
            )
        },
        description = description?.let {
            { Text(text = it) }
        },
        endWidget = {
            if (onClick != null) {
                Spacer(
                    modifier = Modifier
                        .height(32.dp)
                        .width(1.dp)
                        .fillMaxHeight()
                        .background(dividerColor()),
                )
            }
            CheckmarkSwitch(
                checked = checked,
                onCheckedChange = onCheckedChange,
            )
        },
        enabled = enabled,
    )
}

@AILauncherPreview
@Composable
private fun SwitchPreferencePreview() {
    AppTheme {
        SwitchPreference(
            checked = true,
            onCheckedChange = {},
            label = "Example switch",
            description = "Sample description text",
        )
    }
}
