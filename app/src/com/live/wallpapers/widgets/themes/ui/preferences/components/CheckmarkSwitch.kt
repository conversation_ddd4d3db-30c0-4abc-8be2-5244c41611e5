package com.live.wallpapers.widgets.themes.ui.preferences.components

import androidx.compose.animation.animateColor
import androidx.compose.animation.core.animateDp
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.updateTransition
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.live.wallpapers.widgets.themes.ui.theme.LocalCustomColors

@Composable
fun CheckmarkSwitch(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    width: Dp = 44.dp,
    height: Dp = 24.dp,
) {
    val switchColors = LocalCustomColors.current.switchColors

    val transitionDuration = 100

    val transition = updateTransition(checked, label = "switchTransition")
    val thumbSize = height - 4.dp
    val thumbOffset by transition.animateDp(
        transitionSpec = { tween(durationMillis = transitionDuration) },
        label = "thumbOffset",
    ) {
        if (it) width - thumbSize - 2.dp else 0.dp
    }
    val thumbColor by transition.animateColor(
        transitionSpec = { tween(durationMillis = transitionDuration) },
        label = "thumbColor",
    ) {
        if (it) switchColors.checkedThumbColor else switchColors.uncheckedThumbColor
    }
    val backgroundColor by transition.animateColor(
        transitionSpec = { tween(durationMillis = transitionDuration) },
        label = "bgColor",
    ) {
        if (it) switchColors.checkedTrackColor else switchColors.uncheckedTrackColor
    }

    val iconColor = switchColors.checkedTrackColor

    Box(modifier = modifier) {
        Box(
            modifier = Modifier
                .size(width = width, height = height)
                .clip(RoundedCornerShape(height / 2))
                .background(backgroundColor)
                .clickable { onCheckedChange(!checked) }
                .padding(2.dp),
        ) {
            Box(
                modifier = Modifier
                    .offset(x = thumbOffset)
                    .size(thumbSize)
                    .background(thumbColor, CircleShape),
                contentAlignment = Alignment.Center,
            ) {
                if (checked) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "Checked",
                        tint = iconColor,
                        modifier = Modifier.size(thumbSize * 0.7f),
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewCustomSwitch() {
    var isChecked by remember { mutableStateOf(true) }

    CheckmarkSwitch(
        checked = isChecked,
        onCheckedChange = { isChecked = it },
        width = 44.dp,
        height = 24.dp,
    )
}
