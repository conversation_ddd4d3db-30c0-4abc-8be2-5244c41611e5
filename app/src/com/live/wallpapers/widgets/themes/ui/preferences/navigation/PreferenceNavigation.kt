package com.live.wallpapers.widgets.themes.ui.preferences.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navigation
import com.android.launcher3.util.ComponentKey
import com.live.wallpapers.widgets.themes.backup.ui.restoreBackupGraph
import com.live.wallpapers.widgets.themes.preferences.BasePreferenceManager
import com.live.wallpapers.widgets.themes.preferences.preferenceManager
import com.live.wallpapers.widgets.themes.ui.preferences.components.colorpreference.ColorPreferenceModelList
import com.live.wallpapers.widgets.themes.ui.preferences.components.colorpreference.ColorSelection
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.AppDrawerPreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.AppearancePreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.CustomIconShapePreference
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.DebugMenuPreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.DockPreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.DockRoutes
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.DummyPreference
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.ExperimentalFeaturesPreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.FolderPreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.FontSelection
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.GeneralPreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.GeneralRoutes
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.GesturePreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.HiddenAppsPreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.HomeScreenPreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.IconPickerPreference
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.IconShapePreference
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.IconShapeRoutes
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.PickAppForGesture
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.PreferencesDashboard
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.QuickstepPreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.SearchPreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.SearchProviderPreferences
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.SelectIconPreference
import com.live.wallpapers.widgets.themes.ui.preferences.destinations.SmartspacePreferences
import soup.compose.material.motion.animation.materialSharedAxisXIn
import soup.compose.material.motion.animation.materialSharedAxisXOut

@Composable
fun InnerNavigation(
    navController: NavHostController,
    isRtl: Boolean,
    slideDistance: Int,
    isExpandedScreen: Boolean,
) {
    NavHost(
        navController = navController,
        startDestination = if (isExpandedScreen) Routes.GENERAL else "/",
        enterTransition = { materialSharedAxisXIn(!isRtl, slideDistance) },
        exitTransition = { materialSharedAxisXOut(!isRtl, slideDistance) },
        popEnterTransition = { materialSharedAxisXIn(isRtl, slideDistance) },
        popExitTransition = { materialSharedAxisXOut(isRtl, slideDistance) },
    ) {
        composable(route = "/") {
            PreferencesDashboard(
                // Ignore as PreferenceDashboard will not be shown on navigate
                currentRoute = "",
                onNavigate = {
                    navController.navigate(it)
                },
            )
        }
        composable(route = "dummy") {
            DummyPreference()
        }

        navigation(route = Routes.GENERAL, startDestination = "main") {
            composable(route = "main") {
                GeneralPreferences()
            }
            composable(route = GeneralRoutes.HIDDEN_APPS) { HiddenAppsPreferences() }
            composable(
                route = "${Routes.FONT_SELECTION}/{prefKey}",
                arguments = listOf(
                    navArgument("prefKey") { type = NavType.StringType },
                ),
            ) { backStackEntry ->
                val args = backStackEntry.arguments!!
                val prefKey = args.getString("prefKey")!!
                val pref = preferenceManager().prefsMap[prefKey]
                    as? BasePreferenceManager.FontPref ?: return@composable
                FontSelection(pref)
            }
            composable(route = GeneralRoutes.ICON_SHAPE) { IconShapePreference() }
            composable(route = IconShapeRoutes.CUSTOM_ICON_SHAPE_CREATOR) { CustomIconShapePreference() }
        }

        navigation(route = Routes.APPEARANCE, startDestination = "main") {
            composable(route = "main") { AppearancePreferences() }
        }


        navigation(route = Routes.HOME_SCREEN, startDestination = "main") {
            composable(route = "main") { HomeScreenPreferences() }
        }

        navigation(route = Routes.DOCK, startDestination = "main") {
            composable(route = "main") { DockPreferences() }
            composable(route = DockRoutes.SEARCH_PROVIDER) { SearchProviderPreferences() }
        }

        composable(route = Routes.SMARTSPACE) { SmartspacePreferences(fromWidget = false) }
        composable(route = Routes.SMARTSPACE_WIDGET) { SmartspacePreferences(fromWidget = true) }

        navigation(route = Routes.APP_DRAWER, startDestination = "main") {
            composable(route = "main") { AppDrawerPreferences() }
        }

        composable(route = Routes.SEARCH) { SearchPreferences() }
        composable(route = Routes.FOLDERS) { FolderPreferences() }

        composable(route = Routes.GESTURES) { GesturePreferences() }
        composable(route = Routes.PICK_APP_FOR_GESTURE) { PickAppForGesture() }

        composable(route = Routes.QUICKSTEP) { QuickstepPreferences() }

        composable(route = Routes.DEBUG_MENU) { DebugMenuPreferences() }

        composable(
            route = "${Routes.SELECT_ICON}/{packageName}/{nameAndUser}/",
            arguments = listOf(
                navArgument("packageName") { type = NavType.StringType },
                navArgument("nameAndUser") { type = NavType.StringType },
            ),
        ) { backStackEntry ->
            val args = backStackEntry.arguments!!
            val packageName = args.getString("packageName")
            val nameAndUser = args.getString("nameAndUser")
            val key = ComponentKey.fromString("$packageName/$nameAndUser")!!
            SelectIconPreference(key)
        }
        composable(route = Routes.ICON_PICKER) { IconPickerPreference(packageName = "") }
        composable(
            route = "${Routes.ICON_PICKER}/{packageName}",
            arguments = listOf(
                navArgument("packageName") { type = NavType.StringType },
            ),
        ) { backStackEntry ->
            val args = backStackEntry.arguments!!
            val packageName = args.getString("packageName")!!
            IconPickerPreference(packageName)
        }

        composable(route = Routes.EXPERIMENTAL_FEATURES) { ExperimentalFeaturesPreferences() }

        composable(
            route = "${Routes.COLOR_SELECTION}/{prefKey}",
            arguments = listOf(
                navArgument("prefKey") { type = NavType.StringType },
            ),
        ) { backStackEntry ->
            val args = backStackEntry.arguments!!
            val prefKey = args.getString("prefKey")!!
            val modelList = ColorPreferenceModelList.INSTANCE.get(LocalContext.current)
            val model = modelList[prefKey]
            ColorSelection(
                label = stringResource(id = model.labelRes),
                preference = model.prefObject,
                dynamicEntries = model.dynamicEntries,
            )
        }

        restoreBackupGraph(route = Routes.RESTORE_BACKUP)
    }
}

object Routes {
    const val GENERAL = "general"
    const val HOME_SCREEN = "homeScreen"
    const val DOCK = "dock"
    const val APP_DRAWER = "appDrawer"
    const val FOLDERS = "folders"
    const val QUICKSTEP = "quickstep"
    const val FONT_SELECTION = "fontSelection"
    const val COLOR_SELECTION = "colorSelection"
    const val DEBUG_MENU = "debugMenu"
    const val SELECT_ICON = "selectIcon"
    const val ICON_PICKER = "iconPicker"
    const val EXPERIMENTAL_FEATURES = "experimentalFeatures"
    const val SMARTSPACE = "smartspace"
    const val SMARTSPACE_WIDGET = "smartspaceWidget"
    const val RESTORE_BACKUP = "restoreBackup"
    const val PICK_APP_FOR_GESTURE = "pickAppForGesture"
    const val GESTURES = "gestures"
    const val SEARCH = "search"
    const val APPEARANCE = "appearance"
}
