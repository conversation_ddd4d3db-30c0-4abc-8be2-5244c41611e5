package com.live.wallpapers.widgets.themes.preferences2

import android.content.Context
import com.android.launcher3.InvariantDeviceProfile
import com.android.quickstep.TouchInteractionService
import com.android.quickstep.util.TISBindHelper
import com.live.wallpapers.widgets.themes.AILauncher

class ReloadHelper(private val context: Context) {

    private val idp: InvariantDeviceProfile
        get() = InvariantDeviceProfile.INSTANCE.get(context)
    private var tis: TouchInteractionService.TISBinder? = null
    private val tisBinder = TISBindHelper(context) { tis = it }

    fun reloadGrid() {
        idp.onPreferencesChanged(context)
        android.util.Log.d("CustomizeDialog", "reloadGrid ");
    }

    fun recreate() {
        AILauncher.instance?.recreateIfNotScheduled()
    }

    fun restart() {
        reloadGrid()
        recreate()
    }

    fun reloadIcons() {
        idp.onPreferencesChanged(context)
    }

    fun reloadTaskbar() {
        tisBinder.runOnBindToTouchInteractionService {
            tis?.taskbarManager?.onUserPreferenceChanged()
        }
    }
}
