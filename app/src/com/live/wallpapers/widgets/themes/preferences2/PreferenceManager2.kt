package com.live.wallpapers.widgets.themes.preferences2

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.floatPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.core.stringSetPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.android.launcher3.InvariantDeviceProfile
import com.android.launcher3.LauncherAppState
import com.android.launcher3.R
import com.android.launcher3.graphics.IconShape as L3IconShape
import com.android.launcher3.util.DynamicResource
import com.android.launcher3.util.MainThreadInitializedObject
import com.live.wallpapers.widgets.themes.font.FontCache
import com.live.wallpapers.widgets.themes.gestures.config.GestureHandlerConfig
import com.live.wallpapers.widgets.themes.hotseat.HotseatMode
import com.live.wallpapers.widgets.themes.icons.CustomAdaptiveIconDrawable
import com.live.wallpapers.widgets.themes.icons.shape.IconShape
import com.live.wallpapers.widgets.themes.icons.shape.IconShapeManager
import com.live.wallpapers.widgets.themes.preferences.PreferenceManager as AILauncherPreferenceManager
import com.live.wallpapers.widgets.themes.qsb.providers.QsbSearchProvider
import com.live.wallpapers.widgets.themes.search.algorithms.AILauncherSearchAlgorithm
import com.live.wallpapers.widgets.themes.search.algorithms.data.WebSearchProvider
import com.live.wallpapers.widgets.themes.smartspace.model.SmartspaceCalendar
import com.live.wallpapers.widgets.themes.smartspace.model.SmartspaceMode
import com.live.wallpapers.widgets.themes.smartspace.model.SmartspaceTimeFormat
import com.live.wallpapers.widgets.themes.theme.color.ColorMode
import com.live.wallpapers.widgets.themes.theme.color.ColorOption
import com.live.wallpapers.widgets.themes.theme.color.ColorStyle
import com.live.wallpapers.widgets.themes.ui.preferences.components.HiddenAppsInSearch
import com.live.wallpapers.widgets.themes.util.kotlinxJson
import com.patrykmichalik.opto.core.PreferenceManager
import com.patrykmichalik.opto.core.firstBlocking
import com.patrykmichalik.opto.core.setBlocking
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.drop
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.serialization.encodeToString

class PreferenceManager2 private constructor(private val context: Context) : PreferenceManager {

    private val scope = MainScope()
    private val resourceProvider = DynamicResource.provider(context)

    private fun idpPreference(
        key: Preferences.Key<Int>,
        defaultSelector: InvariantDeviceProfile.GridOption.() -> Int,
        onSet: (Int) -> Unit = {},
    ): IdpPreference = IdpPreference(
        key = key,
        defaultSelector = defaultSelector,
        preferencesDataStore = preferencesDataStore,
        onSet = onSet,
    )

    override val preferencesDataStore = context.preferencesDataStore
    private val reloadHelper = ReloadHelper(context)

    val darkStatusBar = preference(
        key = booleanPreferencesKey(name = "dark_status_bar"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_dark_status_bar),
    )

    val hotseatMode = preference(
        key = stringPreferencesKey("hotseat_mode"),
        defaultValue = HotseatMode.fromString(context.getString(R.string.config_default_hotseat_mode)),
        parse = { HotseatMode.fromString(it) },
        save = { it.toString() },
        onSet = { reloadHelper.restart() },
    )

    val iconShape = preference(
        key = stringPreferencesKey(name = "icon_shape"),
        defaultValue = IconShape.fromString(
            value = context.getString(R.string.config_default_icon_shape),
            context = context,
        ) ?: IconShape.RoundedSquare,
        parse = {
            IconShape.fromString(value = it, context = context)
                ?: IconShapeManager.getSystemIconShape(context)
        },
        save = { it.toString() },
    )

    val customIconShape = preference(
        key = stringPreferencesKey(name = "custom_icon_shape"),
        defaultValue = null,
        parse = {
            IconShape.fromString(value = it, context = context)
                ?: IconShapeManager.getSystemIconShape(context)
        },
        save = { it.toString() },
        onSet = { it?.let(iconShape::setBlocking) },
    )

    val alwaysReloadIcons = preference(
        key = booleanPreferencesKey(name = "always_reload_icons"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_always_reload_icons),
    )

    val colorStyle = preference(
        key = stringPreferencesKey("color_style"),
        defaultValue = ColorStyle.fromString("tonal_spot"),
        parse = ColorStyle::fromString,
        save = ColorStyle::toString,
        onSet = { reloadHelper.restart() },
    )

    val strokeColorStyle = preference(
        key = stringPreferencesKey(name = "stroke_color"),
        parse = ColorOption::fromString,
        save = ColorOption::toString,
        onSet = { reloadHelper.restart() },
        defaultValue = ColorOption.fromString(context.getString(R.string.config_default_accent_color)),
    )

    val notificationDotColor = preference(
        key = stringPreferencesKey(name = "notification_dot_color"),
        parse = ColorOption::fromString,
        save = ColorOption::toString,
        onSet = { reloadHelper.reloadGrid() },
        defaultValue = ColorOption.fromString(context.getString(R.string.config_default_notification_dot_color)),
    )

    val notificationDotTextColor = preference(
        key = stringPreferencesKey(name = "notification_dot_text_color"),
        parse = ColorOption::fromString,
        save = ColorOption::toString,
        onSet = { reloadHelper.reloadGrid() },
        defaultValue = ColorOption.fromString(context.getString(R.string.config_default_notification_dot_text_color)),
    )

    val folderColor = preference(
        key = stringPreferencesKey(name = "folder_color"),
        parse = ColorOption::fromString,
        save = ColorOption::toString,
        onSet = { reloadHelper.reloadGrid() },
        defaultValue = ColorOption.fromString(context.getString(R.string.config_default_folder_color)),
    )

    val showNotificationCount = preference(
        key = booleanPreferencesKey(name = "show_notification_count"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_show_notification_count),
        onSet = { reloadHelper.reloadGrid() },
    )

    val themedHotseatQsb = preference(
        key = booleanPreferencesKey(name = "themed_hotseat_qsb"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_themed_hotseat_qsb),
    )

    val isHotseatEnabled = preference(
        key = booleanPreferencesKey(name = "pref_show_hotseat"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_show_hotseat),
        onSet = {
            reloadHelper.recreate()
            reloadHelper.reloadGrid()
        },
    )

    val hotseatQsbProvider = preference(
        key = stringPreferencesKey(name = "dock_search_bar_provider"),
        defaultValue = QsbSearchProvider.resolveDefault(context),
        parse = { QsbSearchProvider.fromId(it) },
        save = { it.id },
        onSet = { reloadHelper.recreate() },
    )

    val hotseatQsbForceWebsite = preference(
        key = booleanPreferencesKey(name = "dock_search_bar_force_website"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_dock_search_bar_force_website),
    )

    val accentColor = preference(
        key = stringPreferencesKey(name = "accent_color"),
        parse = ColorOption::fromString,
        save = ColorOption::toString,
        onSet = { reloadHelper.recreate() },
        defaultValue = ColorOption.fromString(context.getString(R.string.config_default_accent_color)),
    )

    val hiddenApps = preference(
        key = stringSetPreferencesKey(name = "hidden_apps"),
        defaultValue = setOf(),
        onSet = {
            reloadHelper.reloadGrid()
            android.util.Log.d("CustomizeDialog", "reloadGrid 1");
        },
    )

    val roundedWidgets = preference(
        key = booleanPreferencesKey(name = "rounded_widgets"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_rounded_widgets),
        onSet = {
            reloadHelper.reloadGrid()
        },
    )

    val allowWidgetOverlap = preference(
        key = booleanPreferencesKey(name = "allow_widget_overlap"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_allow_widget_overlap),
        onSet = { reloadHelper.reloadGrid() },
    )

    val forceWidgetResize = preference(
        key = booleanPreferencesKey(name = "force_widget_resize"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_force_widget_resize),
    )

    val widgetUnlimitedSize = preference(
        key = booleanPreferencesKey(name = "widget_unlimited_size"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_widget_unlimited_size),
    )

    val showStatusBar = preference(
        key = booleanPreferencesKey(name = "show_status_bar"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_show_status_bar),
    )

    val rememberPosition = preference(
        key = booleanPreferencesKey(name = "all_apps_remember_position"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_remember_position),
        onSet = { reloadHelper.reloadGrid() },
    )

    val showScrollbar = preference(
        key = booleanPreferencesKey(name = "all_apps_show_scrollbar"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_show_scrollbar),
        onSet = { reloadHelper.recreate() },
    )

    val showTopShadow = preference(
        key = booleanPreferencesKey(name = "show_top_shadow"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_show_top_shadow),
    )

    val lockHomeScreen = preference(
        key = booleanPreferencesKey(name = "lock_home_screen"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_lock_home_screen),
    )

    val lockHomeScreenButtonOnPopUp = preference(
        key = booleanPreferencesKey(name = "lock_home_screen_on_popup"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_lock_home_screen_on_popup),
        onSet = { reloadHelper.reloadGrid() },
    )

    val editHomeScreenButtonOnPopUp = preference(
        key = booleanPreferencesKey(name = "edit_home_screen_on_popup"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_edit_home_screen_on_popup),
        onSet = { reloadHelper.reloadGrid() },
    )

    val showSystemSettingsEntryOnPopUp = preference(
        key = booleanPreferencesKey(name = "show_system_settings_entry_on_popup"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_show_system_settings_entry_on_popup),
        onSet = { reloadHelper.reloadGrid() },
    )

    val hideAppDrawerSearchBar = preference(
        key = booleanPreferencesKey(name = "hide_app_drawer_search_bar"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_hide_app_drawer_search_bar),
        onSet = { reloadHelper.recreate() },
    )

    val showHiddenAppsInSearch = preference(
        key = booleanPreferencesKey(name = "show_hidden_apps_in_search"),
        defaultValue = false,
    )

    val enableSmartHide = preference(
        key = booleanPreferencesKey(name = "enable_smart_hide"),
        defaultValue = false,
    )

    val hiddenAppsInSearch = preference(
        key = stringPreferencesKey(name = "hidden_apps_in_search"),
        defaultValue = HiddenAppsInSearch.NEVER,
        onSet = { reloadHelper.recreate() },
    )

    val searchAlgorithm = preference(
        key = stringPreferencesKey(name = "search_algorithm"),
        defaultValue = AILauncherSearchAlgorithm.LOCAL_SEARCH,
        onSet = { reloadHelper.recreate() },
    )

    val showSuggestedAppsInDrawer = preference(
        key = booleanPreferencesKey(name = "show_suggested_apps_at_drawer_top"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_show_suggested_apps_at_drawer_top),
        onSet = { reloadHelper.recreate() },
    )

    val enableFontSelection = preference(
        key = booleanPreferencesKey(name = "enable_font_selection"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_enable_font_selection),
        onSet = { newValue ->
            if (!newValue) {
                val fontCache = FontCache.INSTANCE.get(context)
                AILauncherPreferenceManager.getInstance(context).fontWorkspace.set(newValue = fontCache.uiText)
            }
        },
    )

    val enableSmartspaceCalendarSelection = preference(
        key = booleanPreferencesKey(name = "enable_smartspace_calendar_selection"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_enable_smartspace_calendar_selection),
    )

    val autoShowKeyboardInDrawer = preference(
        key = booleanPreferencesKey(name = "auto_show_keyboard_in_drawer"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_auto_show_keyboard_in_drawer),
    )

    val showRecentAppsInSearch = preference(
        key = booleanPreferencesKey(name = "show_recent_apps_in_search"),
        defaultValue = true,
    )

    val workspaceTextColor = preference(
        key = stringPreferencesKey(name = "workspace_text_color"),
        defaultValue = ColorMode.AUTO,
        parse = { ColorMode.fromString(it) ?: ColorMode.AUTO },
        save = { it.toString() },
        onSet = { reloadHelper.recreate() },
    )

    val homeTextColor = preference(
        key = stringPreferencesKey(name = "home_text_color"),
        defaultValue = "#FFFFFF",
        onSet = { reloadHelper.recreate() },
    )

    val homeIconSizeFactor = preference(
        key = floatPreferencesKey(name = "home_icon_size_factor"),
        defaultValue = resourceProvider.getFloat(R.dimen.config_default_home_icon_size_factor),
        onSet = { reloadHelper.reloadIcons() },
    )

    val showIconPreviewFolder = preference(
        key = booleanPreferencesKey(name = "show_icon_preview_folder"),
        defaultValue = true,
        onSet = { reloadHelper.reloadIcons() },
    )

    val folderPreviewBackgroundOpacity = preference(
        key = floatPreferencesKey(name = "folder_preview_background_opacity"),
        defaultValue = resourceProvider.getFloat(R.dimen.config_default_folder_preview_background_opacity),
        onSet = { reloadHelper.reloadIcons() },
    )

    val folderBackgroundOpacity = preference(
        key = floatPreferencesKey(name = "folder_background_opacity"),
        defaultValue = resourceProvider.getFloat(R.dimen.config_default_folder_background_opacity),
        onSet = { reloadHelper.reloadIcons() },
    )

    val showIconLabelsOnHomeScreen = preference(
        key = booleanPreferencesKey(name = "show_icon_labels_on_home_screen"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_show_icon_labels_on_home_screen),
        onSet = { reloadHelper.reloadGrid() },
    )

    val showIconLabelsOnHomeScreenFolder = preference(
        key = booleanPreferencesKey(name = "show_icon_labels_on_home_screen_folder"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_show_icon_labels_on_home_screen),
        onSet = { reloadHelper.reloadGrid() },
    )

    val drawerIconSizeFactor = preference(
        key = floatPreferencesKey(name = "drawer_icon_size_factor"),
        defaultValue = resourceProvider.getFloat(R.dimen.config_default_drawer_icon_size_factor),
        onSet = { reloadHelper.reloadIcons() },
    )

    val showIconLabelsInDrawer = preference(
        key = booleanPreferencesKey(name = "show_icon_labels_in_drawer"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_show_icon_labels_in_drawer),
        onSet = { reloadHelper.reloadGrid() },
    )

    val homeIconLabelSizeFactor = preference(
        key = floatPreferencesKey(name = "home_icon_label_size_factor"),
        defaultValue = resourceProvider.getFloat(R.dimen.config_default_home_icon_label_size_factor),
        onSet = { reloadHelper.reloadGrid() },
    )

    val homeIconLabelFolderSizeFactor = preference(
        key = floatPreferencesKey(name = "home_icon_label_folder_size_factor"),
        defaultValue = resourceProvider.getFloat(R.dimen.config_default_home_icon_label_folder_size_factor),
        onSet = { reloadHelper.reloadGrid() },
    )

    val pageIndicatorHeightFactor = preference(
        key = floatPreferencesKey(name = "page_indicator_height_factor"),
        defaultValue = resourceProvider.getFloat(R.dimen.page_indicator_height_factor),
        onSet = { reloadHelper.reloadGrid() },
    )

    val autoAdjustTextSize = preference(
        key = booleanPreferencesKey(name = "auto_adjust_text_size"),
        defaultValue = true,
        onSet = { reloadHelper.reloadGrid() },
    )

    val drawerIconLabelSizeFactor = preference(
        key = floatPreferencesKey(name = "drawer_icon_label_size_factor"),
        defaultValue = resourceProvider.getFloat(R.dimen.config_default_drawer_icon_label_size_factor),
        onSet = { reloadHelper.reloadGrid() },
    )

    val drawerCellHeightFactor = preference(
        key = floatPreferencesKey(name = "drawer_cell_height_factor"),
        defaultValue = resourceProvider.getFloat(R.dimen.config_default_drawer_cell_height_factor),
        onSet = { reloadHelper.reloadGrid() },
    )

    val drawerLeftRightMarginFactor = preference(
        key = floatPreferencesKey(name = "drawer_left_right_factor"),
        defaultValue = resourceProvider.getFloat(R.dimen.config_default_drawer_left_right_factor),
        onSet = { reloadHelper.reloadGrid() },
    )

    val hotseatBottomFactor = preference(
        key = floatPreferencesKey(name = "hotseat_bottom_factor"),
        defaultValue = resourceProvider.getFloat(R.dimen.config_default_hotseat_bottom_factor),
        onSet = { reloadHelper.reloadGrid() },
    )

    val enableFuzzySearch = preference(
        key = booleanPreferencesKey(name = "enable_fuzzy_search"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_enable_fuzzy_search),
    )

    val matchHotseatQsbStyle = preference(
        key = booleanPreferencesKey(name = "use_drawer_search_icon"),
        defaultValue = false,
        onSet = { reloadHelper.recreate() },
    )

    val webSuggestionProvider = preference(
        key = stringPreferencesKey(name = "web_suggestion_provider"),
        defaultValue = WebSearchProvider.fromString(context.resources.getString(R.string.config_default_web_suggestion_provider)),
        parse = { WebSearchProvider.fromString(it) },
        save = { it.toString() },
        onSet = { reloadHelper.recreate() },
    )

    val maxAppSearchResultCount = preference(
        key = intPreferencesKey(name = "max_search_result_count"),
        defaultValue = resourceProvider.getInt(R.dimen.config_default_search_max_result_count),
    )

    val maxWebSuggestionResultCount = preference(
        key = intPreferencesKey(name = "max_suggestion_result_count"),
        defaultValue = resourceProvider.getInt(R.dimen.config_default_suggestion_max_result_count),
    )

    val maxFileResultCount = preference(
        key = intPreferencesKey(name = "max_files_result_count"),
        defaultValue = resourceProvider.getInt(R.dimen.config_default_files_max_result_count),
    )

    val maxPeopleResultCount = preference(
        key = intPreferencesKey(name = "max_people_result_count"),
        defaultValue = resourceProvider.getInt(R.dimen.config_default_people_max_result_count),
    )

    val maxWebSuggestionDelay = preference(
        key = intPreferencesKey(name = "max_web_suggestion_delay"),
        defaultValue = resourceProvider.getInt(R.dimen.config_default_max_web_suggestion_delay),
    )

    val maxSettingsEntryResultCount = preference(
        key = intPreferencesKey(name = "max_settings_entry_result_count"),
        defaultValue = resourceProvider.getInt(R.dimen.config_default_settings_entry_max_result_count),
    )

    val maxRecentResultCount = preference(
        key = intPreferencesKey(name = "max_recent_result_count"),
        defaultValue = resourceProvider.getInt(R.dimen.config_default_recent_max_result_count),
    )

    val enableSmartspace = preference(
        key = booleanPreferencesKey(name = "enable_smartspace"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_enable_smartspace),
        onSet = { reloadHelper.restart() },
    )

    val enableDotPagination = preference(
        key = booleanPreferencesKey(name = "enable_dot_pagination"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_enable_dot_pagination),
        onSet = { reloadHelper.recreate() },
    )

    val enableMaterialUPopUp = preference(
        key = booleanPreferencesKey(name = "enable_material_u_popup"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_enable_material_u_popup),
        onSet = { reloadHelper.recreate() },
    )

    val twoLineAllApps = preference(
        key = booleanPreferencesKey(name = "two_line_all_apps"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_enable_two_line_allapps),
        onSet = { reloadHelper.recreate() },
    )

    val enableFeed = preference(
        key = booleanPreferencesKey(name = "enable_feed"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_enable_feed),
    )

    val showComponentNames = preference(
        key = booleanPreferencesKey(name = "show_component_names"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_show_component_names),
    )

    val drawerColumns = idpPreference(
        key = intPreferencesKey(name = "drawer_columns"),
        defaultSelector = { numAllAppsColumns },
        onSet = { reloadHelper.reloadGrid() },
    )

    val folderColumns = idpPreference(
        key = intPreferencesKey(name = "folder_columns"),
        defaultSelector = { numFolderColumns },
        onSet = { reloadHelper.reloadGrid() },
    )

    val additionalFonts = preference(
        key = stringPreferencesKey(name = "additional_fonts"),
        defaultValue = "",
    )

    val enableTaskbarOnPhone = preference(
        key = booleanPreferencesKey("enable_taskbar_on_phone"),
        defaultValue = false,
        onSet = {
            reloadHelper.reloadGrid()
            reloadHelper.reloadTaskbar()
            reloadHelper.recreate()
        },
    )

    val smartspaceMode = preference(
        key = stringPreferencesKey("smartspace_mode"),
        defaultValue = SmartspaceMode.fromString(context.getString(R.string.config_default_smartspace_mode)),
        parse = { SmartspaceMode.fromString(it) },
        save = { it.toString() },
        onSet = { reloadHelper.recreate() },
    )

    val smartspaceAagWidget = preference(
        key = booleanPreferencesKey("enable_smartspace_aag_widget"),
        defaultValue = true,
    )

    val smartspaceBatteryStatus = preference(
        key = booleanPreferencesKey("enable_smartspace_battery_status"),
        defaultValue = false,
    )

    val smartspaceNowPlaying = preference(
        key = booleanPreferencesKey("enable_smartspace_now_playing"),
        defaultValue = true,
    )

    val smartspaceShowDate = preference(
        key = booleanPreferencesKey("smartspace_show_date"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_smartspace_show_date),
    )

    val smartspaceShowTime = preference(
        key = booleanPreferencesKey("smartspace_show_time"),
        defaultValue = context.resources.getBoolean(R.bool.config_default_smartspace_show_time),
    )

    val smartspaceTimeFormat = preference(
        key = stringPreferencesKey("smartspace_time_format"),
        defaultValue = SmartspaceTimeFormat.fromString(context.getString(R.string.config_default_smartspace_time_format)),
        parse = { SmartspaceTimeFormat.fromString(it) },
        save = { it.toString() },
    )

    val smartspaceCalendar = preference(
        key = stringPreferencesKey(name = "smartspace_calendar"),
        defaultValue = SmartspaceCalendar.fromString(context.getString(R.string.config_default_smart_space_calendar)),
        parse = { SmartspaceCalendar.fromString(it) },
        save = { it.toString() },
    )

    val smartspacerMaxCount = preference(
        key = intPreferencesKey(name = "smartspace_max_count"),
        defaultValue = 5,
        onSet = { reloadHelper.recreate() },
    )

    val wallpaperDepthEffect = preference(
        key = booleanPreferencesKey(name = "enable_wallpaper_depth_effect"),
        defaultValue = true,
        onSet = { reloadHelper.recreate() },
    )

    val enableLabelInDock = preference(
        key = booleanPreferencesKey(name = "enable_label_dock"),
        defaultValue = false,
        onSet = { reloadHelper.reloadGrid() },
    )

    val doubleTapGestureHandler = serializablePreference<GestureHandlerConfig>(
        key = stringPreferencesKey("double_tap_gesture_handler"),
        defaultValue = GestureHandlerConfig.NoOp,
    )

    val swipeUpGestureHandler = serializablePreference<GestureHandlerConfig>(
        key = stringPreferencesKey("swipe_up_gesture_handler"),
        defaultValue = GestureHandlerConfig.OpenAppDrawer,
    )

    val swipeDownGestureHandler = serializablePreference<GestureHandlerConfig>(
        key = stringPreferencesKey("swipe_down_gesture_handler"),
        defaultValue = GestureHandlerConfig.OpenAppSearch,
    )

    val homePressGestureHandler = serializablePreference<GestureHandlerConfig>(
        key = stringPreferencesKey("home_press_gesture_handler"),
        defaultValue = GestureHandlerConfig.NoOp,
    )

    val backPressGestureHandler = serializablePreference<GestureHandlerConfig>(
        key = stringPreferencesKey("back_press_gesture_handler"),
        defaultValue = GestureHandlerConfig.NoOp,
    )

    private inline fun <reified T> serializablePreference(
        key: Preferences.Key<String>,
        defaultValue: T,
    ) = preference(
        key = key,
        defaultValue = defaultValue,
        parse = { value ->
            runCatching { kotlinxJson.decodeFromString<T>(value) }.getOrDefault(defaultValue)
        },
        save = kotlinxJson::encodeToString,
    )

    init {
        initializeIconShape(iconShape.firstBlocking())
        iconShape.get()
            .drop(1)
            .distinctUntilChanged()
            .onEach { shape ->
                initializeIconShape(shape)
                L3IconShape.init(context)
                LauncherAppState.getInstance(context).reloadIcons()
            }
            .launchIn(scope)
    }

    private fun initializeIconShape(shape: IconShape) {
        CustomAdaptiveIconDrawable.sInitialized = true
        CustomAdaptiveIconDrawable.sMaskId = shape.getHashString()
        CustomAdaptiveIconDrawable.sMask = shape.getMaskPath()
    }

    companion object {
        private val Context.preferencesDataStore by preferencesDataStore(
            name = "preferences",
            produceMigrations = { listOf(SharedPreferencesMigration(context = it).produceMigration()) },
        )

        @JvmField
        val INSTANCE = MainThreadInitializedObject(::PreferenceManager2)

        @JvmStatic
        fun getInstance(context: Context) = INSTANCE.get(context)!!
    }
}

@Composable
fun preferenceManager2() = PreferenceManager2.getInstance(LocalContext.current)
