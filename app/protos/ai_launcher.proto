syntax = "proto3";

option java_package = "com.live.wallpapers.widgets.themes";
option java_outer_classname = "AILauncherProto";

import "google/protobuf/timestamp.proto";

message BackupInfo {
    int32 launcher_version = 1;
    int32 backup_version = 2;
    google.protobuf.Timestamp created_at = 3;

    int32 contents = 4;
    int32 preview_width = 5;
    int32 preview_height = 6;
    bool preview_dark_text = 8;
    GridState grid_state = 7;
}

message GridState {
    string grid_size = 1;
    int32 hotseat_count = 2;
    int32 device_type = 3;
}
