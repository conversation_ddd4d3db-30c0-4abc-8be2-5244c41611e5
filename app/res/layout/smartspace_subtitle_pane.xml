<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.live.wallpapers.widgets.themes.smartspace.DoubleShadowTextView
        android:id="@+id/subtitle_text"
        style="@style/EnhancedSmartspaceTextSubtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?android:attr/textColorPrimary"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@+id/weather_container"
        app:layout_constraintEnd_toStartOf="@+id/base_action_icon_subtitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone" />

    <com.live.wallpapers.widgets.themes.smartspace.DoubleShadowTextView
        android:id="@+id/base_action_icon_subtitle"
        style="@style/EnhancedSmartspaceTextSubtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/enhanced_smartspace_icon_margin"
        android:ellipsize="none"
        android:textColor="?android:attr/textColorPrimary"
        app:layout_constraintBaseline_toBaselineOf="@+id/subtitle_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/subtitle_text"
        app:layout_constraintWidth_min="wrap"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/weather_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/subtitle_text">

        <TextView
            android:id="@+id/weather_summary_text"
            style="@style/EnhancedSmartspaceTextSubtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:textSize="22sp"
            android:textColor="#FFFFFFFF"
            android:textStyle="bold"
            android:shadowColor="#FF000000"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="4" />

        <ImageView
            android:id="@+id/weather_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/weather_missing"
            android:contentDescription="@string/weather_icon_description" />

    </LinearLayout>
</merge>
