<?xml version="1.0" encoding="utf-8"?>
<com.live.wallpapers.widgets.themes.allapps.AllAppsSearchInput xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/search_container_all_apps"
    android:layout_width="match_parent"
    android:layout_height="@dimen/search_box_container_height"
    android:clickable="true"
    android:focusable="true">
    <FrameLayout
        android:id="@+id/search_wrapper"
        android:layout_width="match_parent"
        android:layout_height="@dimen/search_box_height"
        android:layout_gravity="bottom"
        android:background="@drawable/search_input_fg"
        android:clickable="false"
        android:focusable="true"
        android:padding="0dp">
        <TextView
            android:id="@+id/hint"
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:layout_marginStart="28dp"
            android:layout_marginEnd="28dp"
            android:clickable="false"
            android:ellipsize="end"
            android:gravity="start|center"
            android:letterSpacing="-0.01"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:textColor="?android:textColorTertiary"
            android:textSize="20sp" />

        <com.live.wallpapers.widgets.themes.allapps.FallbackSearchInputView
            android:id="@+id/input"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="28dp"
            android:layout_marginEnd="100dp"
            android:background="@android:color/transparent"
            android:ellipsize="end"
            android:focusableInTouchMode="true"
            android:gravity="start|center"
            android:imeOptions="actionGo|flagNoExtractUi"
            android:inputType="textNoSuggestions"
            android:letterSpacing="-0.01"
            android:privateImeOptions="bc_search"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:textColor="?android:textColorPrimary"
            android:textColorHint="?android:textColorTertiary"
            android:textSize="20sp" />

    </FrameLayout>

    <ImageButton
        android:id="@+id/search_icon"
        android:layout_width="@dimen/search_box_height"
        android:layout_height="@dimen/search_box_height"
        android:layout_gravity="bottom|center|start"
        android:background="@drawable/pill_ripple"
        android:layout_marginEnd="6dp"
        android:clickable="true"
        android:contentDescription="@string/search_bar_label"
        android:scaleType="center"
        android:src="@drawable/ic_allapps_search"
        app:tint="?android:textColorSecondary" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/search_box_height"
        android:layout_gravity="bottom|end|center"
        android:clipToPadding="false"
        android:orientation="horizontal"
        tools:ignore="RtlSymmetry">

        <com.live.wallpapers.widgets.themes.qsb.AssistantIconView
            android:id="@+id/mic_btn"
            android:layout_width="@dimen/search_box_height"
            android:layout_height="@dimen/search_box_height"
            android:background="@drawable/pill_ripple"
            android:clickable="true"
            android:contentDescription="@string/label_voice_search"
            android:scaleType="center"
            android:src="@drawable/ic_remove_no_shadow"
            app:tint="?android:textColorSecondary" />

        <ImageButton
            android:id="@+id/lens_btn"
            android:layout_width="@dimen/search_box_height"
            android:layout_height="@dimen/search_box_height"
            android:background="@drawable/pill_ripple"
            android:clickable="true"
            android:contentDescription="@string/label_lens"
            android:scaleType="center"
            android:src="@drawable/ic_remove_no_shadow"
            app:tint="?android:textColorSecondary" />

        <ImageButton
            android:id="@+id/action_btn"
            android:layout_width="@dimen/search_box_height"
            android:layout_height="@dimen/search_box_height"
            android:background="@drawable/pill_ripple"
            android:clickable="true"
            android:contentDescription="@string/search_input_action_clear_results"
            android:scaleType="center"
            android:src="@drawable/ic_remove_no_shadow"
            app:tint="?android:textColorSecondary" />

        <TextView
            android:id="@+id/cancel_btn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/pill_ripple"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:paddingHorizontal="8dp"
            android:layout_marginStart="-4dp"
            android:text="@string/desktop_button_close_app_toast"
            android:textAllCaps="false"
            android:textColor="?android:textColorSecondary"
            android:textSize="16sp" />
    </LinearLayout>

</com.live.wallpapers.widgets.themes.allapps.AllAppsSearchInput>
