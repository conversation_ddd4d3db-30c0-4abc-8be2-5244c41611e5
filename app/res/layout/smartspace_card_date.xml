<?xml version="1.0" encoding="utf-8"?>
<com.live.wallpapers.widgets.themes.smartspace.BcSmartspaceCard xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:baselineAligned="false"
    android:paddingTop="@dimen/enhanced_smartspace_padding_top">

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="3"
        android:clipChildren="false"
        android:clipToPadding="false">

        <com.live.wallpapers.widgets.themes.smartspace.DigitalClockView
            android:id="@+id/analogClockView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_alignParentTop="true"
            android:gravity="center"/>

        <com.live.wallpapers.widgets.themes.smartspace.IcuDateTextView
            android:id="@+id/date"
            style="@style/EnhancedSmartspaceTextTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/analogClockView"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="8dp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/smartspace_subtitle_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/date"
            android:layout_marginTop="8dp"
            android:layout_centerHorizontal="true">

            <include layout="@layout/smartspace_subtitle_pane" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/smartspace_extras_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/smartspace_subtitle_group"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:visibility="invisible">

            <include layout="@layout/smartspace_extras" />
        </LinearLayout>
    </RelativeLayout>
</com.live.wallpapers.widgets.themes.smartspace.BcSmartspaceCard>
