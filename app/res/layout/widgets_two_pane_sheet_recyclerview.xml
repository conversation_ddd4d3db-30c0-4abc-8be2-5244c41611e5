<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:launcher="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:id="@+id/widgets_two_pane_sheet_recyclerview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="start"
        android:layout_gravity="start"
        android:layout_alignParentStart="true">

        <com.android.launcher3.widget.picker.WidgetsRecyclerView
            android:id="@+id/primary_widgets_list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/widget_list_horizontal_margin_two_pane"
            android:clipToPadding="false" />

        <!-- SearchAndRecommendationsView without the tab layout as well -->
        <com.android.launcher3.views.StickyHeaderLayout
            android:id="@+id/search_and_recommendations_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToOutline="true"
            android:orientation="vertical">

            <FrameLayout
                android:id="@+id/search_bar_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/widgetPickerPrimarySurfaceColor"
                android:clipToPadding="false"
                android:elevation="0.1dp"
                android:paddingBottom="8dp"
                android:paddingHorizontal="@dimen/widget_list_horizontal_margin_two_pane"
                launcher:layout_sticky="true">

                <include layout="@layout/widgets_search_bar" />
            </FrameLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/suggestions_header"
                android:layout_marginTop="8dp"
                android:layout_marginHorizontal="@dimen/widget_list_horizontal_margin_two_pane"
                android:paddingBottom="16dp"
                android:orientation="horizontal"
                android:background="?attr/widgetPickerPrimarySurfaceColor"
                launcher:layout_sticky="true">
            </LinearLayout>
        </com.android.launcher3.views.StickyHeaderLayout>
    </FrameLayout>
</merge>