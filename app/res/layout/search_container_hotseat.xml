<?xml version="1.0" encoding="utf-8"?>
<com.live.wallpapers.widgets.themes.qsb.LauncherQsbLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/qsb_widget_height"
    android:layout_alignParentTop="true"
    android:layout_centerHorizontal="true"
    android:layout_gravity="center|top"
    android:contentDescription="@string/label_search"
    android:paddingVertical="@dimen/qsb_widget_vertical_padding">

    <FrameLayout
        android:id="@+id/inner"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:foreground="?android:attr/selectableItemBackground">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="end|center"
            android:clipToPadding="false"
            android:orientation="horizontal"
            tools:ignore="RtlSymmetry">

            <com.live.wallpapers.widgets.themes.qsb.AssistantIconView
                android:id="@+id/mic_icon"
                android:layout_width="@dimen/qsb_icon_width"
                android:layout_height="match_parent"
                android:clickable="true"
                android:contentDescription="@string/label_voice_search"
                android:focusable="false"
                android:foreground="?android:attr/selectableItemBackground"
                android:padding="@dimen/qsb_icon_padding"
                android:scaleType="fitCenter"
                tools:src="@drawable/ic_lens_color" />

            <ImageButton
                android:id="@+id/lens_icon"
                android:layout_width="@dimen/qsb_icon_width"
                android:layout_height="match_parent"
                android:layout_marginStart="-6dp"
                android:clickable="true"
                android:contentDescription="@string/label_lens"
                android:focusable="false"
                android:foreground="?android:attr/selectableItemBackground"
                android:padding="@dimen/qsb_icon_padding"
                android:scaleType="fitCenter"
                android:visibility="gone"
                tools:src="@drawable/ic_lens_color"
                tools:visibility="visible" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/search_section"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|start"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginStart="@dimen/qsb_g_icon_marginStart">

            <ImageView
                android:id="@+id/g_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_super_g_color" />

            <TextView
                android:id="@+id/myTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="@string/search_hint"
                android:textSize="16sp"
                android:textColor="?attr/popupTextColor" />
        </LinearLayout>

    </FrameLayout>

</com.live.wallpapers.widgets.themes.qsb.LauncherQsbLayout>
