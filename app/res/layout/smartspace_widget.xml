<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.live.wallpapers.widgets.themes.smartspace.BcSmartspaceView
        android:id="@+id/bc_smartspace_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/enhanced_smartspace_margin_start_launcher">

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/smartspace_card_pager"
            android:layout_width="match_parent"
            android:layout_height="@dimen/enhanced_smartspace_height"
            android:layout_gravity="center" />

        <com.live.wallpapers.widgets.themes.smartspace.PageIndicator
            android:id="@+id/smartspace_page_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|left|center_vertical|center_horizontal|center|start"
            android:importantForAccessibility="no"
            android:paddingStart="@dimen/page_indicator_padding_start"
            android:paddingTop="@dimen/page_indicator_padding_top_bottom"
            android:paddingBottom="@dimen/page_indicator_padding_top_bottom"
            android:visibility="visible" />
    </com.live.wallpapers.widgets.themes.smartspace.BcSmartspaceView>
</FrameLayout>
