<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:launcher="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:id="@+id/widgets_two_pane_sheet_paged_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="start"
        android:paddingHorizontal="@dimen/widget_list_horizontal_margin_two_pane"
        android:layout_gravity="start"
        android:layout_alignParentStart="true">
        <com.android.launcher3.widget.picker.WidgetPagedView
            android:id="@+id/widgets_view_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:descendantFocusability="afterDescendants"
            launcher:pageIndicator="@+id/tabs" >

            <com.android.launcher3.widget.picker.WidgetsRecyclerView
                android:id="@+id/primary_widgets_list_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false" />

            <com.android.launcher3.widget.picker.WidgetsRecyclerView
                android:id="@+id/work_widgets_list_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false" />

        </com.android.launcher3.widget.picker.WidgetPagedView>

        <!-- SearchAndRecommendationsView without the tab layout as well -->
        <com.android.launcher3.views.StickyHeaderLayout
            android:id="@+id/search_and_recommendations_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToOutline="true"
            android:orientation="vertical">

            <FrameLayout
                android:id="@+id/search_bar_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/widgetPickerPrimarySurfaceColor"
                android:clipToPadding="false"
                android:elevation="0.1dp"
                android:paddingBottom="8dp"
                launcher:layout_sticky="true">

                <include layout="@layout/widgets_search_bar" />
            </FrameLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/suggestions_header"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:background="?attr/widgetPickerPrimarySurfaceColor"
                launcher:layout_sticky="true">
            </LinearLayout>

            <com.android.launcher3.workprofile.PersonalWorkSlidingTabStrip
                android:id="@+id/tabs"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:paddingVertical="8dp"
                android:background="?attr/widgetPickerPrimarySurfaceColor"
                style="@style/TextHeadline"
                launcher:layout_sticky="true">

                <Button
                    android:id="@+id/tab_personal"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/widget_tabs_button_horizontal_padding"
                    android:layout_marginVertical="@dimen/widget_apps_tabs_vertical_padding"
                    android:layout_weight="1"
                    android:background="@drawable/widget_picker_tabs_background"
                    android:text="@string/widgets_full_sheet_personal_tab"
                    android:textColor="@color/widget_picker_tab_text"
                    android:textSize="14sp"
                    style="?android:attr/borderlessButtonStyle" />

                <Button
                    android:id="@+id/tab_work"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/widget_tabs_button_horizontal_padding"
                    android:layout_marginVertical="@dimen/widget_apps_tabs_vertical_padding"
                    android:layout_weight="1"
                    android:background="@drawable/widget_picker_tabs_background"
                    android:text="@string/widgets_full_sheet_work_tab"
                    android:textColor="@color/widget_picker_tab_text"
                    android:textSize="14sp"
                    style="?android:attr/borderlessButtonStyle" />

            </com.android.launcher3.workprofile.PersonalWorkSlidingTabStrip>
        </com.android.launcher3.views.StickyHeaderLayout>
    </FrameLayout>
</merge>
