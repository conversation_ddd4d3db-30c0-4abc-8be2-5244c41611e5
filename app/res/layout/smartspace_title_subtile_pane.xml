<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <Space
        android:id="@+id/top_padding"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@+id/title_text"
        app:layout_constraintHeight_max="@dimen/enhanced_smartspace_padding_top"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_weight="1" />

    <com.live.wallpapers.widgets.themes.smartspace.DoubleShadowTextView
        android:id="@+id/title_text"
        style="@style/EnhancedSmartspaceTextTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@+id/smartspace_subtitle_group"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/top_padding" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/smartspace_subtitle_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/enhanced_smartspace_subtitle_margin_top"
        app:layout_constraintBottom_toTopOf="@+id/smartspace_extras_group"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_text">

        <include layout="@layout/smartspace_subtitle_pane" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/smartspace_extras_group"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:gravity="top"
        android:orientation="horizontal"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_min="wrap"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/smartspace_subtitle_group">

        <include layout="@layout/smartspace_extras" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
