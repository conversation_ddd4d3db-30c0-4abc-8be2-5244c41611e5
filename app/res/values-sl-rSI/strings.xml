<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- TODO: Rename all strings to follow CONTRIBUTING.md guidelines -->
    <!--

    Actions and Verbs

    -->
    <string name="preview_label">Predogled</string>
    <string name="action_create">Ustvari</string>
    <string name="action_backup">Backup</string>
    <string name="action_restore">Obnovi</string>
    <string name="action_delete"><PERSON><PERSON><PERSON><PERSON>i</string>
    <string name="action_reset">Ponastavi</string>
    <string name="action_unlock">Unlock</string>
    <string name="action_lock">Lock</string>
    <string name="action_apply">Uporabi</string>
    <string name="action_customize">Prilagodi</string>
    <string name="always_choice">Always</string>
    <string name="never_choice">Never</string>
    <string name="clipboard">Clipboard</string>
    <string name="action_copy_link">Copy link</string>
    <string name="action_copy"><PERSON><PERSON><PERSON></string>
    <string name="action_paste">Paste</string>
    <string name="loading">Na<PERSON><PERSON>je…</string>
    <string name="managed_by_ai_launcher">Managed by Revive Launcher</string>
    <!-- When mentioning settings UI -->
    <string name="smartspace_preferences">Preference</string>
    <string name="settings_button_text">Home settings</string>
    <string name="system_settings">System settings</string>
    <string name="title_change_settings">Change settings</string>
    <!--

    General "words" used throughout Revive Launcher

    -->
    <!-- Selection options -->
    <string name="dynamic">Dinamično</string>
    <string name="presets">Prednastavitve</string>
    <string name="custom">Po meri</string>
    <string name="feed_default">Default</string>
    <string name="system">Sistem</string>
    <!-- Relating to the Launcher ui -->
    <string name="columns">Stolpci</string>
    <string name="rows">Vrstice</string>
    <string name="label">Oznaka</string>
    <string name="icons">Ikone</string>
    <string name="grid">Mreža</string>
    <string name="layout">Postavitev</string>
    <!-- Generic styling options -->
    <string name="wallpaper">Ozadje</string>
    <string name="background_opacity">Background opacity</string>
    <!-- Toast text and tips -->
    <string name="copied_toast">Copied to clipboard</string>
    <string name="item_removed">Item removed</string>
    <!-- Miscellaneous -->
    <string name="what_to_show">What to show</string>
    <!-- A11y description -->
    <string name="x_by_y">%1$d x %2$d</string>
    <string name="x_and_y">%1$s &amp; %2$s</string>
    <!--

    Preference Dashboard

    -->
    <string name="settings">Nastavitve</string>
    <string name="general_label">Splošno</string>
    <string name="general_description">Colors, icon packs, notification dots</string>
    <string name="home_screen_label">Home screen</string>
    <string name="home_screen_description">Feed, grid, icons</string>
    <string name="dock_label">Vrstica za aplikacije</string>
    <string name="dock_description">Search bar, icon count</string>
    <string name="app_drawer_label">App drawer</string>
    <string name="app_drawer_description">Hidden apps, column count, icons</string>
    <string name="drawer_search_label">Drawer search</string>
    <string name="drawer_search_description">Web suggestions, global search</string>
    <string name="folders_label">Mape</string>
    <string name="folders_description">Row and column count</string>
    <string name="gestures_label">Gestures</string>
    <string name="gestures_description">Taps and swipes</string>
    <string name="quickstep_label">Nedavno</string>
    <string name="quickstep_description">Clear All button, corner radius</string>
    <string name="about_label">O tem</string>
    <string name="app_info_drop_target_label">App info</string>
    <string name="debug_restart_launcher">Resetiraj Revive Launcher</string>
    <string name="experimental_features_label">Experimental features</string>
    <!-- Experimental features -->
    <string name="font_picker_label">Font customization</string>
    <string name="font_picker_description">Some text remains unchanged</string>
    <string name="smartspace_calendar_label">At a Glance calendar customization</string>
    <string name="smartspace_calendar_description">Allow showing date in non-Gregorian calendar systems</string>
    <string name="workspace_increase_max_grid_size_label">Increase maximum grid size limit</string>
    <string name="workspace_increase_max_grid_size_description">Increase maximum allowed home screen grid size from 10 x 10 to 20 x 20</string>
    <string name="always_reload_icons_label">Always reload icons</string>
    <string name="always_reload_icons_description">Avoid using cached icons from icon packs</string>
    <string name="recents_lock_unlock">Lock/unlock</string>
    <string name="recents_lock_unlock_description">Prevent selected app from closing when pressing \"Clear all\"</string>
    <!--

    Notifications

    -->
    <string name="set_default_launcher_tip">To access shortcuts and additional features, set Revive Launcher as your default launcher</string>
    <string name="notification_dots">Notification dots</string>
    <string name="show_notification_count">Show notification counter</string>
    <string name="notification_dots_color">Notification dot color</string>
    <string name="notification_dots_text_color">Notification counter color</string>
    <string name="notification_dots_color_contrast_warning_always">Warning: Notification dot and counter colors don\'t have enough contrast with each other</string>
    <string name="notification_dots_color_contrast_warning_sometimes">Warning: Notification dot and counter colors might not always have enough contrast with each other</string>
    <string name="missing_notification_access_label">Notification access needed</string>
    <string name="missing_notification_access_desc">To show Notification Dots, turn on app notifications for <xliff:g id="name" example="My App">%1$s</xliff:g></string>
    <!--

    Icons

    -->
    <!-- General strings -->
    <string name="icon_style_label">Icon style</string>
    <string name="icon_shape_label">Icon shape</string>
    <string name="icon_sizes">Icon size</string>
    <string name="show_labels">Show labels</string>
    <string name="label_size">Label size</string>
    <string name="twoline_label">Use multiple lines</string>
    <!-- Icon-related settings -->
    <string name="transparent_background_icons_label">Transparent themed icons</string>
    <string name="transparent_background_icons_description">Use transparent background on themed icons</string>
    <string name="auto_adaptive_icons_label">Auto-adaptive icons</string>
    <string name="auto_adaptive_icons_description">For all non-adaptive icons</string>
    <string name="shadow_bg_icons_label">Show shadow behind icons</string>
    <string name="background_lightness_label">Background lightness</string>
    <string name="adaptive_icon_background_description">Use 100% background lightness for white</string>
    <string name="reset_custom_icons">Reset custom icons</string>
    <string name="reset_custom_icons_confirmation">All custom icons will be reset. Do you want to continue?</string>
    <!-- Icon picker -->
    <string name="icon_picker_default_category">Ikone</string>
    <string name="icon_picker_reset_to_default">Reset to default</string>
    <string name="icon_pack_external_picker">Open external picker</string>
    <string name="pick_icon_from_label">Pick icon from</string>
    <string name="icon_picker_load_failed">Couldn\'t load more icons</string>
    <!-- Icon shapes  -->
    <string name="icon_shape_system">Sistem</string>
    <string name="icon_shape_circle">Krog</string>
    <string name="icon_shape_cylinder">Cilinder</string>
    <string name="icon_shape_diamond">Diamond</string>
    <string name="icon_shape_egg">Egg</string>
    <string name="icon_shape_cupertino">iOS</string>
    <string name="icon_shape_octagon">Octagon</string>
    <string name="icon_shape_hexagon">Hexagon</string>
    <string name="icon_shape_sammy">One UI</string>
    <string name="icon_shape_rounded_square">Rounded square</string>
    <string name="icon_shape_sharp_square">Sharp square</string>
    <string name="icon_shape_square">Kvadrat</string>
    <string name="icon_shape_squircle">Squircle</string>
    <string name="icon_shape_teardrop">Teardrop</string>
    <!-- Custom icon shapes -->
    <string name="custom_icon_shape">Custom icon shape</string>
    <string name="custom_icon_shape_create">Create custom icon shape</string>
    <string name="custom_icon_shape_edit">Edit custom icon shape</string>
    <string name="custom_icon_shape_corner">Corner shape</string>
    <string name="custom_icon_shape_corner_round">Round</string>
    <string name="custom_icon_shape_corner_squircle">Smooth</string>
    <string name="custom_icon_shape_corner_cut">Cut</string>
    <string name="custom_icon_shape_top_left">Top left</string>
    <string name="custom_icon_shape_top_right">Top right</string>
    <string name="custom_icon_shape_bottom_left">Bottom left</string>
    <string name="custom_icon_shape_bottom_right">Bottom right</string>
    <string name="export_to_clipboard">Export to clipboard</string>
    <string name="import_from_clipboard">Import from clipboard</string>
    <string name="icon_shape_clipboard_import_error">Clipboard doesn\'t contain a valid icon shape</string>
    <!-- Icon pack settings -->
    <string name="icon_pack">Icon pack</string>
    <string name="themed_icon_pack">Themed icon source</string>
    <string name="system_icons">System icons</string>
    <string name="themed_icon_title">Themed icons</string>
    <string name="themed_icons_off_label">Izklopljen</string>
    <string name="themed_icons_home_label">Home screen</string>
    <string name="themed_icons_home_and_drawer_label">Home screen &amp; app drawer</string>
    <!-- Fonts -->
    <string name="pref_fonts_add_fonts">Add fonts</string>
    <string name="pref_fonts_add_fonts_summary">OTF and TTF fonts are supported</string>
    <string name="pref_fonts_missing_font">Font not found</string>
    <string name="font_label">Font (experimental)</string>
    <string name="fontWorkspace">Splošno</string>
    <string name="fontHeading">Headings</string>
    <string name="fontHeadingMedium">Headings (medium)</string>
    <string name="fontBody">Body</string>
    <string name="fontBodyMedium">Body (medium)</string>
    <string name="font_variant_italic">Kurzivna</string>
    <string name="font_weight_thin">Tanek</string>
    <string name="font_weight_extra_light">Extra light</string>
    <string name="font_weight_light">Svetlo</string>
    <string name="font_weight_regular">Navaden</string>
    <string name="font_weight_medium">Medium</string>
    <string name="font_weight_semi_bold">Semibold</string>
    <string name="font_weight_bold">Bold</string>
    <string name="font_weight_extra_bold">Extra bold</string>
    <string name="font_weight_extra_black">Black</string>
    <!--

    Colors and theme

    -->
    <!-- Theme -->
    <string name="theme_label">Tema</string>
    <string name="theme_light">Svetlo</string>
    <string name="theme_dark">Temno</string>
    <string name="theme_system_default">Sistem</string>
    <string name="theme_follow_wallpaper">Match wallpaper</string>
    <!-- Color style -->
    <string name="color_style_label">Color style</string>
    <string name="color_style_spritz">Spritz</string>
    <string name="color_style_tonal_spot">Tonal Spot</string>
    <string name="color_style_vibrant">Vibrant</string>
    <string name="color_style_expressive">Expressive</string>
    <string name="color_style_rainbow">Rainbow</string>
    <string name="color_style_fruit_salad">Fruit Salad</string>
    <string name="color_style_content">Content</string>
    <string name="color_style_monochromatic">Monochromatic</string>
    <!-- Accent color and color picker -->
    <string name="colors">Barve</string>
    <string name="accent_color">Accent color</string>
    <string name="swatches">Swatches</string>
    <string name="rgb">RGB</string>
    <string name="rgb_red">Red</string>
    <string name="rgb_green">Green</string>
    <string name="rgb_blue">Blue</string>
    <string name="hsb">HSB</string>
    <string name="hsb_hue">Hue</string>
    <string name="hsb_saturation">Saturation</string>
    <string name="hsb_brightness">Brightness</string>
    <string name="hex">Hex</string>
    <string name="color_sliders">Sliders</string>
    <string name="invalid_color">Invalid color</string>
    <!--

    Smartspace

    -->
    <!-- Date formats -->
    <string name="smartspace_calendar_gregorian">Gregorijanski</string>
    <string name="smartspace_calendar_persian">Perzijski</string>
    <string name="generic_smartspace_concatenated_desc">%1$s, %2$s</string>
    <!-- Battery labels -->
    <string name="smartspace_battery_charging">Polnjenje</string>
    <string name="smartspace_battery_full">Napolnjeno</string>
    <string name="smartspace_battery_low">Battery low</string>
    <string name="battery_charging_percentage_charging_time">"%1$d%% — Full in %2$s"</string>
    <string name="smartspace_widget">Na pogled</string>
    <string name="smartspace_widget_description">What to show</string>
    <!-- Data and time format settings -->
    <string name="smartspace_calendar">Koledar</string>
    <string name="smartspace_date_and_time">Date &amp; time</string>
    <string name="smartspace_date">Datum</string>
    <string name="smartspace_time">Ura</string>
    <string name="smartspace_time_format">Time format</string>
    <string name="smartspace_time_follow_system">Follow system</string>
    <string name="smartspace_time_12_hour_format">12-hour format</string>
    <string name="smartspace_time_24_hour_format">24-hour format</string>
    <!-- Available targets -->
    <string name="smartspace_weather">Vreme</string>
    <string name="smartspace_battery_status">Battery status</string>
    <string name="smartspace_now_playing">Trenutno se predvaja</string>
    <!-- Smartspacer strings -->
    <string name="maximum_number_of_targets">Maximum number of targets</string>
    <string name="open_smartspacer_settings">Open Smartspacer settings</string>
    <string name="smartspacer_settings">Smartspacer settings</string>
    <!-- Setup settings -->
    <string name="smartspace_requires_setup">Tap to set up</string>
    <string name="event_provider_missing_notification_dots">To use <xliff:g example="My Provider" id="providerName">%1$s</xliff:g>, turn on Notification Dots.</string>
    <!-- Toggle button for enabling Smartspace -->
    <string name="smartspace_widget_toggle_label">Show on home screen</string>
    <string name="smartspace_widget_toggle_description">At a Glance can be manually added to the home screen by placing the Revive Launcher widget</string>
    <!-- List of available providers -->
    <string name="smartspace_mode_label">At a Glance provider</string>
    <string name="smartspace_mode_google">Google</string>
    <string name="smartspace_mode_google_search">Google Search</string>
    <!-- Miscellaneous Smartspace strings -->
    <string name="smartspace_media_info_separator">" — "</string>
    <string name="accessibility_smartspace_page">Stran %1$d od %2$d</string>
    <string name="smartspace_widget_placeholder_date">Pet, Mar 3</string>
    <!--

    About the app

    -->
    <string name="news">Novice</string>
    <string name="support">Podpora</string>
    <string name="product">Produkt</string>
    <string name="design_and_development">Design &amp; development</string>
    <string name="development">Razvijanje</string>
    <string name="quickswitch_maintenance">QuickSwitch maintenance</string>
    <string name="devops">DevOps</string>
    <string name="support_and_pr">Podpirajte &amp; PR</string>
    <string name="acknowledgements">Zahvale</string>
    <string name="translate">Prevod</string>
    <string name="donate">Donate</string>
    <!--

    Backup and restore

    -->
    <string name="create_backup">Create backup</string>
    <string name="what_to_backup">What to back up</string>
    <string name="backup_content_layout_and_settings">Layout and settings</string>
    <string name="backup_content_wallpaper">Ozadje</string>
    <string name="backup_create_success">Backup created</string>
    <string name="backup_create_error">Failed to create backup</string>
    <string name="restore_backup">Restore backup</string>
    <string name="what_to_restore">What to restore</string>
    <string name="backup_restore_success">Backup restored</string>
    <string name="backup_restore_error">Failed to restore backup</string>
    <string name="invalid_backup_file">Invalid backup file</string>
    <!--

    Gesture settings

    -->
    <string name="gesture_double_tap">Double tap</string>
    <string name="gesture_swipe_up">Swipe up</string>
    <string name="gesture_swipe_down">Swipe down</string>
    <string name="gesture_home_tap">Home button</string>
    <string name="gesture_back_tap">Back button</string>
    <string name="gesture_handler_no_op">Do nothing</string>
    <string name="gesture_handler_sleep">Spanje</string>
    <string name="gesture_handler_recents">Open Recents</string>
    <string name="gesture_handler_open_notifications">Open notification panel</string>
    <string name="gesture_handler_open_app_option">Open app</string>
    <string name="gesture_handler_open_app_config">Open %1$s</string>
    <string name="gesture_handler_open_app_drawer">Open app drawer</string>
    <string name="gesture_handler_open_app_search">Open app search</string>
    <string name="gesture_handler_open_search">Open search</string>
    <string name="pick_app_for_gesture">Pick app</string>
    <!--

    Bug reporting

    -->
    <string name="ai_launcher_bug_report">Revive Launcher bug report</string>
    <string name="crash_report_notif_title">%1$s crashed</string>
    <string name="action_upload_crash_report">Upload crash log</string>
    <string name="action_upload_error">Prenos neuspešen</string>
    <string name="dogbin_uploading">Prenašanje…</string>
    <string name="bugreport_channel_name">Bug reports</string>
    <string name="status_channel_name">Upload status</string>
    <string name="bugreport_group_summary">%d new reports</string>
    <string name="bugreport_group_summary_multiple">Multiple new reports</string>
    <!--

    Preferences (without special screens)

    -->
    <!-- General and home screen settings -->
    <string name="home_screen_rotation_label">Home screen rotation</string>
    <string name="home_screen_rotation_description">Allow home screen rotation when device is rotated</string>
    <string name="wallpaper_blur">Blur wallpaper (experimental)</string>
    <string name="wallpaper_background_blur">Blur intensity</string>
    <string name="wallpaper_background_blur_factor">Factor threshold</string>
    <string name="auto_add_shortcuts_label">Add new apps to home screen</string>
    <string name="minus_one_enable">Show feed</string>
    <string name="minus_one_unavailable">No feed apps installed</string>
    <string name="minus_one">Feed</string>
    <string name="feed_provider">Feed provider</string>
    <string name="wallpaper_scrolling_label">Scroll wallpaper</string>
    <string name="wallpaper_depth_effect_label">Wallpaper depth effect</string>
    <string name="wallpaper_depth_effect_description">Zoom in and out of the wallpaper when transitioning between areas of the launcher</string>
    <string name="show_sys_ui_scrim">Top shadow</string>
    <string name="home_screen_grid">Home screen grid</string>
    <string name="home_screen_lock">Lock home screen</string>
    <string name="home_screen_unlock">Unlock home screen</string>
    <string name="home_screen_locked">Home screen is locked</string>
    <string name="home_screen_lock_description">Prevent changes to the home screen layout</string>
    <string name="show_dot_pagination_label">Show dot pagination</string>
    <string name="show_dot_pagination_description">Use dots instead of lines to show page number</string>
    <string name="show_material_u_popup_label">Use new pop-up style</string>
    <string name="show_material_u_popup_description">Use Material You\'s bouncy and slightly consolidated pop-up style</string>
    <string name="popup_menu">Pop-up menu</string>
    <string name="home_screen_lock_toggle_from_home_popup">Show lock button</string>
    <string name="show_system_settings_entry">Show system settings button</string>
    <string name="home_screen_edit_toggle_from_home_popup">Show edit home screen button</string>
    <string name="status_bar_label">Status bar</string>
    <string name="show_status_bar">Show status bar</string>
    <string name="dark_status_bar_label">Dark status bar</string>
    <string name="home_screen_text_color">Text color</string>
    <string name="color_light">Svetlo</string>
    <string name="color_dark">Temno</string>
    <string name="force_rounded_widgets">Rounded corners</string>
    <string name="allow_widget_overlap">Allow overlap</string>
    <string name="force_widget_resize_label">Enforce widget resizing</string>
    <string name="force_widget_resize_description">Allow resizing of widgets that are constrained to a specific size</string>
    <string name="widget_unlimited_size_label">Remove size constraints</string>
    <string name="widget_unlimited_size_description">Remove the minimum and maximum size restrictions of widgets</string>
    <!-- Dock settings -->
    <string name="show_hotseat_title">Show dock</string>
    <string name="search_bar_label">Search bar</string>
    <string name="hotseat_mode_label">Search bar widget</string>
    <string name="hotseat_mode_disabled">Disabled</string>
    <string name="hotseat_mode_google_search">Google Search bar</string>
    <string name="qsb_hotseat_background_transparency">Background opacity</string>
    <string name="qsb_hotseat_stroke_width">Outline width</string>
    <string name="qsb_hotseat_stroke_color">Outline color</string>
    <string name="corner_radius_label">Polmer robov</string>
    <string name="apply_accent_color_label">Apply accent color</string>
    <string name="search_provider">Search provider</string>
    <string name="dock_icons">Dock icons</string>
    <string name="hotseat_bottom_space_label">Bottom padding</string>
    <!-- Search providers -->
    <string name="search_provider_app_search">App search</string>
    <string name="search_provider_sponsored_description">%1$s and Revive Launcher have a revenue share agreement.\n\nSearching with %1$s helps support Revive Launcher.</string>
    <string name="app_label">App</string>
    <string name="website_label">Website</string>
    <string name="qsb_search_provider_app_required">App required</string>
    <!-- Dock search bar a11y -->
    <string name="label_search">Išči</string>
    <string name="label_lens">Google Lens</string>
    <string name="label_voice_search">Glasovno iskanje</string>
    <!-- App drawer settings -->
    <string name="hidden_apps_label">Hidden apps</string>
    <string name="pref_all_apps_bulk_icon_loading_title">Load apps in bulk</string>
    <string name="pref_all_apps_bulk_icon_loading_description">Load and display icons in bulk instead of individually</string>
    <string name="pref_all_apps_remember_position_title">Remember position</string>
    <string name="pref_all_apps_remember_position_description">Remember app drawer position after leaving drawer</string>
    <string name="pref_all_apps_show_scrollbar_title">Show scrollbar</string>
    <string name="app_drawer_columns">App drawer columns</string>
    <string name="row_height_label">Row height</string>
    <string name="app_drawer_indent_label">Horizontal padding</string>
    <!-- HiddenAppsPreferences -->
    <string name="hide_from_drawer">Hide from app drawer</string>
    <string name="hidden_apps_label_with_count">Hidden apps (%1$d)</string>
    <plurals name="apps_count">
        <item quantity="one">%1$d app</item>
        <item quantity="two">%1$d apps</item>
        <item quantity="few">%1$d apps</item>
        <item quantity="other">%1$d apps</item>
    </plurals>
    <!-- Folder settings -->
    <string name="folder_preview_bg_opacity_label">Icon preview background opacity</string>
    <string name="folder_bg_opacity_label">Folder background opacity</string>
    <string name="folder_preview_bg_color_label">Icon background color</string>
    <string name="max_folder_columns">Maximum folder columns</string>
    <string name="max_folder_rows">Maximum folder rows</string>
    <!-- Quickstep settings -->
    <string name="quickswitch_ignored_warning">These settings will be ignored as Revive Launcher isn\'t set as the Recents provider</string>
    <string name="quickstep_incompatible">Incompatible system integration</string>
    <string name="quickstep_incompatible_description">Your device is configured to have system gestures (known as Quickstep) provided by %1$s, but this version of %1$s isn\'t compatible with your Android version. To continue using your device, please uninstall %1$s updates or disable %1$s as a system gesture provider.</string>
    <string name="translucent_background">Translucent background</string>
    <string name="translucent_background_alpha">Background opacity</string>
    <string name="recents_actions_label">Quick actions</string>
    <string name="action_share">Deli</string>
    <string name="action_lens">Leča</string>
    <string name="recents_clear_all">Clear all</string>
    <string name="task_menu_force_stop">Force close</string>
    <string name="window_corner_radius_label">Screen corner radius</string>
    <string name="override_window_corner_radius_label">Custom screen corner radius</string>
    <string name="window_corner_radius_description">When you swipe up to open Recents, the current app follows your finger, shrinking into a card. Use this slider to adjust the corner radius of the card when it\'s nearly full screen so it matches the corners of your screen.</string>
    <string name="taskbar_label">Opravilna vrstica</string>
    <string name="enable_taskbar_experimental">Show taskbar (experimental)</string>
    <!--

    All Apps search

    -->
    <!-- Launcher strings used -->
    <string name="all_apps_device_search_hint">Išči</string>
    <string name="all_apps_search_bar_hint">Iskanje aplikacij</string>
    <string name="all_apps_no_search_results">No apps found matching \"<xliff:g example="Android" id="query">%1$s</xliff:g>\"</string>
    <string name="all_apps_search_result_suggestions">From the web</string>
    <string name="all_apps_search_result_contacts_from_device">Contacts from device</string>
    <string name="all_apps_search_result_files">Files from device</string>
    <string name="all_apps_search_result_settings_entry_from_device">Settings from device</string>
    <string name="all_apps_search_market_message">Search for more apps</string>
    <string name="all_apps_search_on_web_message">Search on <xliff:g example="Startpage" id="web_search_provider">%1$s</xliff:g></string>
    <string name="error_no_market_or_browser_installed">No app store or browser installed</string>
    <string name="clear_history">Clear search history</string>
    <string name="search_input_action_clear_results">Clear search box</string>
    <!-- Search settings -->
    <string name="pref_category_search">Išči</string>
    <string name="show_app_search_bar">Show search bar</string>
    <string name="pref_search_auto_show_keyboard">Automatically show keyboard</string>
    <string name="fuzzy_search_title">Fuzzy search</string>
    <string name="fuzzy_search_desc">Approximate matching for app searches</string>
    <string name="suggestion_pref_screen_title">Predlogi</string>
    <string name="show_suggested_apps_at_drawer_top">Show suggested apps at the top of the drawer</string>
    <string name="perform_wide_search_title">Device search</string>
    <string name="perform_wide_search_description">Search your phone contacts, files, and settings</string>
    <string name="show_hidden_apps_in_search_results">Show hidden apps in search results</string>
    <string name="hidden_apps_show_name_typed">If full name is typed</string>
    <string name="app_search_algorithm">Search algorithm</string>
    <string name="search_algorithm_app_search">App Search</string>
    <string name="search_algorithm_global_search_on_device">Global search (on-device)</string>
    <string name="search_algorithm_global_search_via_asi">Global search (via ASI)</string>
    <!-- Labels of each search result types -->
    <string name="show_search_result_types">Show in search results</string>
    <string name="search_pref_result_apps_and_shortcuts_title">Apps &amp; Shortcuts</string>
    <string name="search_pref_result_shortcuts_title">App shortcuts</string>
    <string name="search_pref_result_people_title">Ljudje</string>
    <string name="search_pref_result_tips_title">Pixel tips</string>
    <string name="search_pref_result_settings_title">Android settings</string>
    <string name="search_pref_result_files_title">Files</string>
    <string name="search_pref_result_web_title">Web suggestions</string>
    <string name="search_pref_result_history_title">Search history</string>
    <string name="all_apps_search_result_calculator">Calculator</string>
    <!-- Description for each search result type -->
    <string name="search_pref_result_files_description">Media, files, and more</string>
    <string name="search_pref_result_contacts_description">Contacts and more</string>
    <string name="search_pref_result_web_provider_description">Via <xliff:g id="web_search_provider">%1$s</xliff:g></string>
    <!-- Maximum xyz for each search result type -->
    <string name="max_apps_result_count_title">Maximum number of apps</string>
    <string name="max_people_result_count_title">Maximum number of people</string>
    <string name="max_file_result_count_title">Maximum number of files</string>
    <string name="max_settings_entry_result_count_title">Maximum number of settings</string>
    <string name="max_recent_result_count_title">Maximum items for search history</string>
    <string name="max_suggestion_result_count_title">Maximum number of suggestions</string>
    <string name="max_web_suggestion_delay">Maximum web suggestion delay</string>
    <!-- Permission warnings -->
    <string name="warn_contact_permission_content">To search for contacts, grant contacts and phone permissions to Revive Launcher</string>
    <string name="warn_files_permission_content">To search your files, grant storage permissions to Revive Launcher</string>
    <string name="grant_requested_permissions">Grant permissions</string>
    <string name="allapps_web_suggestion_provider_label">Web suggestion provider</string>
    <string name="allapps_use_web_suggestion_icon_label">Show web suggestion provider icon in search bar</string>
    <string name="allapps_match_qsb_style_label">Match dock search bar actions</string>
    <string name="allapps_match_qsb_style_description">Clicking the dock search bar will now open the app drawer search UI</string>
</resources>
