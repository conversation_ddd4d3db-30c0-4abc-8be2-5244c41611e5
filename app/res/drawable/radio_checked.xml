<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- H<PERSON>nh tròn bên ngoài -->
    <item>
        <shape android:shape="oval">
            <size android:width="24dp" android:height="24dp" />
            <solid android:color="@color/gnt_blue" />
        </shape>
    </item>
    
    <!-- <PERSON><PERSON><PERSON><PERSON> trống (gap) - dùng màu nền để tạo vòng tròn trong -->
    <item android:gravity="center">
        <shape android:shape="oval">
            <size android:width="18dp" android:height="18dp" />
            <solid android:color="@color/ads_native_background" />
        </shape>
    </item>
    
    <!-- H<PERSON>nh tròn bên trong -->
    <item android:gravity="center">
        <shape android:shape="oval">
            <size android:width="10dp" android:height="10dp" />
            <solid android:color="@color/gnt_blue" />
        </shape>
    </item>
</layer-list>
