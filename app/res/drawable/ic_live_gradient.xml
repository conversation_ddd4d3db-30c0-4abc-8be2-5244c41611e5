<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="21dp"
    android:height="19dp"
    android:viewportWidth="21"
    android:viewportHeight="19">
  <path
      android:pathData="M18.375,5.25C18.273,5.25 18.182,5.184 18.152,5.086L17.794,3.955L16.663,3.598C16.446,3.529 16.446,3.22 16.663,3.151L17.794,2.794L18.152,1.663C18.22,1.446 18.53,1.446 18.598,1.663L18.956,2.794L20.087,3.151C20.304,3.22 20.304,3.529 20.087,3.598L18.956,3.955L18.598,5.086C18.568,5.184 18.477,5.25 18.375,5.25Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.5"
          android:startY="3.375"
          android:endX="20.25"
          android:endY="3.375"
          android:type="linear">
        <item android:offset="0" android:color="#FF6275FF"/>
        <item android:offset="1" android:color="#FF3CFF52"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M10.336,7.215C9.813,6.893 9.386,6.63 9.034,6.457C8.68,6.284 8.312,6.152 7.925,6.196C7.364,6.259 6.867,6.571 6.549,7.037C6.333,7.354 6.258,7.738 6.223,8.147C6.187,8.554 6.187,9.076 6.188,9.723V9.777C6.187,10.424 6.187,10.946 6.223,11.353C6.258,11.762 6.333,12.146 6.549,12.463C6.867,12.929 7.364,13.241 7.925,13.304C8.312,13.347 8.68,13.216 9.034,13.043C9.386,12.87 9.813,12.607 10.336,12.285L10.385,12.255C10.908,11.933 11.335,11.67 11.651,11.432C11.972,11.19 12.25,10.923 12.4,10.566C12.617,10.046 12.617,9.454 12.4,8.934C12.25,8.577 11.972,8.31 11.651,8.068C11.335,7.83 10.908,7.567 10.385,7.245L10.336,7.215ZM8.05,7.314C8.109,7.307 8.236,7.319 8.538,7.467C8.836,7.614 9.217,7.848 9.771,8.188C10.325,8.529 10.706,8.764 10.974,8.967C11.243,9.169 11.328,9.288 11.361,9.368C11.463,9.61 11.463,9.89 11.361,10.132C11.328,10.212 11.243,10.331 10.974,10.533C10.706,10.736 10.325,10.971 9.771,11.312C9.217,11.653 8.836,11.886 8.538,12.033C8.236,12.181 8.109,12.193 8.05,12.186C7.831,12.162 7.621,12.038 7.479,11.829C7.431,11.759 7.374,11.615 7.343,11.257C7.313,10.903 7.313,10.43 7.313,9.75C7.313,9.07 7.313,8.596 7.343,8.243C7.374,7.885 7.431,7.741 7.479,7.671C7.621,7.462 7.831,7.338 8.05,7.314Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1.688"
          android:startY="9.75"
          android:endX="16.313"
          android:endY="9.75"
          android:type="linear">
        <item android:offset="0" android:color="#FF6275FF"/>
        <item android:offset="1" android:color="#FF3CFF52"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M12.348,2.53C11.491,2.437 10.411,2.437 9.034,2.438H8.966C7.589,2.437 6.509,2.437 5.652,2.53C4.778,2.625 4.07,2.822 3.465,3.261C3.099,3.527 2.777,3.849 2.511,4.215C2.072,4.82 1.875,5.528 1.78,6.402C1.687,7.259 1.687,8.339 1.688,9.716V9.784C1.687,11.161 1.687,12.241 1.78,13.098C1.875,13.972 2.072,14.68 2.511,15.285C2.777,15.651 3.099,15.973 3.465,16.239C4.07,16.678 4.778,16.875 5.652,16.97C6.509,17.063 7.589,17.063 8.966,17.063H9.034C10.411,17.063 11.491,17.063 12.348,16.97C13.222,16.875 13.93,16.678 14.535,16.239C14.901,15.973 15.223,15.651 15.489,15.285C15.928,14.68 16.125,13.972 16.22,13.098C16.313,12.241 16.313,11.161 16.313,9.784V9.716C16.313,8.339 16.313,7.259 16.22,6.402C16.125,5.528 15.928,4.82 15.489,4.215C15.223,3.849 14.901,3.527 14.535,3.261C13.93,2.822 13.222,2.625 12.348,2.53ZM4.126,4.171C4.507,3.894 4.995,3.733 5.774,3.649C6.562,3.563 7.581,3.563 9,3.563C10.419,3.563 11.438,3.563 12.226,3.649C13.005,3.733 13.493,3.894 13.874,4.171C14.144,4.368 14.382,4.606 14.579,4.876C14.856,5.257 15.017,5.745 15.101,6.524C15.187,7.312 15.188,8.331 15.188,9.75C15.188,11.169 15.187,12.188 15.101,12.976C15.017,13.755 14.856,14.243 14.579,14.624C14.382,14.894 14.144,15.132 13.874,15.329C13.493,15.606 13.005,15.767 12.226,15.851C11.438,15.937 10.419,15.938 9,15.938C7.581,15.938 6.562,15.937 5.774,15.851C4.995,15.767 4.507,15.606 4.126,15.329C3.856,15.132 3.618,14.894 3.421,14.624C3.144,14.243 2.983,13.755 2.899,12.976C2.813,12.188 2.813,11.169 2.813,9.75C2.813,8.331 2.813,7.312 2.899,6.524C2.983,5.745 3.144,5.257 3.421,4.876C3.618,4.606 3.856,4.368 4.126,4.171Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1.688"
          android:startY="9.75"
          android:endX="16.313"
          android:endY="9.75"
          android:type="linear">
        <item android:offset="0" android:color="#FF6275FF"/>
        <item android:offset="1" android:color="#FF3CFF52"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M15.75,2.25C15.709,2.25 15.673,2.224 15.661,2.185L15.518,1.732L15.065,1.589C14.978,1.562 14.978,1.438 15.065,1.41L15.518,1.268L15.661,0.815C15.688,0.728 15.812,0.728 15.839,0.815L15.982,1.268L16.435,1.41C16.522,1.438 16.522,1.562 16.435,1.589L15.982,1.732L15.839,2.184C15.827,2.223 15.791,2.25 15.75,2.25Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="15"
          android:startY="1.5"
          android:endX="16.5"
          android:endY="1.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF6275FF"/>
        <item android:offset="1" android:color="#FF3CFF52"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
