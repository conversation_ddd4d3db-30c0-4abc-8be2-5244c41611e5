<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="18dp"
    android:height="18dp"
    android:viewportWidth="18"
    android:viewportHeight="18">
  <path
      android:pathData="M9.094,15.304C8.801,15.304 8.397,15.357 8.296,15.226L2.053,7.156C1.952,7.025 2.046,6.833 2.212,6.833L15.337,6.833C15.502,6.833 15.597,7.025 15.495,7.156L9.252,15.226C9.214,15.275 9.156,15.304 9.094,15.304Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="8.774"
          android:startY="6.833"
          android:endX="8.774"
          android:endY="15.315"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFB52C"/>
        <item android:offset="1" android:color="#FFFFAD16"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M9.094,15.304C8.801,15.304 8.397,15.357 8.296,15.226L8.189,15.087L4.353,6.833L8.77,6.833L13.191,6.833L9.359,15.088L9.252,15.226C9.214,15.275 9.155,15.304 9.094,15.304Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="8.772"
          android:startY="6.833"
          android:endX="8.772"
          android:endY="15.315"
          android:type="linear">
        <item android:offset="0" android:color="#FFFDD776"/>
        <item android:offset="1" android:color="#FFFFEDBE"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M9.064,15.304C8.875,15.306 8.647,15.326 8.484,15.304L6.231,7.065C6.212,6.943 6.306,6.833 6.429,6.833H11.119C11.242,6.833 11.336,6.943 11.317,7.065L9.064,15.304Z"
      android:fillColor="#FDC25F"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M15.336,7.234H2.212C2.049,7.234 1.954,7.05 2.048,6.917L4.319,3.725C4.356,3.672 4.417,3.641 4.482,3.641H13.066C13.131,3.641 13.192,3.672 13.23,3.725L15.5,6.918C15.594,7.05 15.498,7.234 15.336,7.234Z"
      android:fillColor="#FDC25F"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M12.043,7.234H5.501L4.256,3.812L4.319,3.725C4.356,3.672 4.417,3.641 4.482,3.641H13.066C13.131,3.641 13.192,3.672 13.23,3.725L13.288,3.808L12.043,7.234Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="8.772"
          android:startY="3.641"
          android:endX="8.772"
          android:endY="7.234"
          android:type="linear">
        <item android:offset="0" android:color="#FFFDD776"/>
        <item android:offset="1" android:color="#FFFFEDBE"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M12.043,7.234H5.501L5.383,6.909C5.967,6.129 7.263,4.553 8.039,3.641H9.477L12.17,6.883L12.043,7.234Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="8.777"
          android:startY="3.641"
          android:endX="8.777"
          android:endY="7.234"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFB52C"/>
        <item android:offset="1" android:color="#FFFFAD16"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M1.35,5.4C1.276,5.4 1.211,5.352 1.189,5.282L0.932,4.468L0.117,4.21C-0.039,4.161 -0.039,3.938 0.117,3.889L0.932,3.632L1.189,2.817C1.238,2.661 1.461,2.661 1.511,2.817L1.768,3.632L2.583,3.889C2.739,3.938 2.739,4.161 2.583,4.21L1.768,4.468L1.511,5.282C1.489,5.352 1.424,5.4 1.35,5.4Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1.35"
          android:startY="2.7"
          android:endX="1.35"
          android:endY="5.4"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFB52C"/>
        <item android:offset="1" android:color="#FFFFAD16"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M3.655,3.209C3.567,3.209 3.49,3.152 3.463,3.069L3.158,2.101L2.19,1.795C2.004,1.736 2.004,1.472 2.19,1.413L3.158,1.107L3.463,0.139C3.522,-0.046 3.787,-0.046 3.846,0.139L4.151,1.107L5.119,1.413C5.305,1.472 5.305,1.736 5.119,1.795L4.151,2.101L3.846,3.069C3.819,3.152 3.742,3.209 3.655,3.209Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="3.655"
          android:startY="0"
          android:endX="3.655"
          android:endY="3.209"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFB52C"/>
        <item android:offset="1" android:color="#FFFFAD16"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M13.448,14.21C13.361,14.21 13.283,14.153 13.257,14.069L12.951,13.101L11.984,12.796C11.797,12.737 11.798,12.472 11.984,12.414L12.951,12.108L13.257,11.14C13.316,10.954 13.581,10.954 13.639,11.14L13.945,12.108L14.913,12.414C15.099,12.472 15.099,12.737 14.913,12.796L13.945,13.101L13.639,14.069C13.613,14.153 13.535,14.21 13.448,14.21Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="13.448"
          android:startY="11.001"
          android:endX="13.448"
          android:endY="14.21"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFB52C"/>
        <item android:offset="1" android:color="#FFFFAD16"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M15.499,12.077C15.411,12.077 15.334,12.02 15.308,11.937L15.002,10.969L14.034,10.663C13.848,10.605 13.848,10.34 14.034,10.281L15.002,9.976L15.308,9.008C15.366,8.822 15.631,8.822 15.69,9.008L15.996,9.976L16.963,10.281C17.15,10.34 17.149,10.605 16.963,10.663L15.996,10.969L15.69,11.937C15.664,12.02 15.586,12.077 15.499,12.077Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="15.499"
          android:startY="8.868"
          android:endX="15.499"
          android:endY="12.077"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFB52C"/>
        <item android:offset="1" android:color="#FFFFAD16"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
