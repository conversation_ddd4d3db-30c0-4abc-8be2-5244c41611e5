<?xml version="1.0" encoding="utf-8"?>
<com.android.quickstep.views.DesktopTaskView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="true"
    android:clipToOutline="true"
    android:defaultFocusHighlightEnabled="false"
    android:focusable="true">

    <View
        android:id="@+id/background"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!--
         TODO(b249371338): DesktopTaskView extends from TaskView. TaskView expects TaskThumbnailView
         and IconView with these ids to be present. Need to refactor RecentsView to accept child
         views that do not inherint from TaskView only or create a generic TaskView that have
         N number of tasks.
     -->
    <com.android.quickstep.views.TaskThumbnailView
        android:id="@+id/snapshot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <com.android.quickstep.views.IconView
        android:id="@+id/icon"
        android:layout_width="@dimen/task_thumbnail_icon_size"
        android:layout_height="@dimen/task_thumbnail_icon_size"
        android:focusable="false"
        android:importantForAccessibility="no" />

</com.android.quickstep.views.DesktopTaskView>
