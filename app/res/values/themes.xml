<?xml version="1.0" encoding="utf-8"?>

<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="Theme.AILauncher" parent="Base.Theme.AILauncher">
        <!-- Let the activity handle icon colors -->
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar" tools:ignore="NewApi">false</item>
    </style>

    <style name="Base.Theme.AILauncher" parent="Base.Theme.MaterialThemeBuilder">
        <item name="android:colorBackground">@color/white_50</item>
    </style>

    <style name="Base.Theme.MaterialThemeBuilder" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:statusBarColor" tools:ignore="NewApi">?android:attr/colorBackground</item>
        <item name="android:windowLightStatusBar" tools:ignore="NewApi">true</item>
        <item name="android:navigationBarColor" tools:ignore="NewApi">?android:attr/colorBackground</item>
        <item name="android:windowLightNavigationBar" tools:ignore="NewApi">true</item>
    </style>

    <style name="Theme.Transparent" parent="Theme.AppCompat.Light">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">false</item>
        <item name="windowNoTitle">true</item>
    </style>
</resources>
