<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_light">@color/system_accent1_500</color>
    <color name="accent_dark">@color/system_accent1_200</color>
    <color name="dark_drawer_text_color">#FFFFFFFF</color>
    <color name="light_drawer_text_color">#DE000000</color>

    <color name="system_neutral1_0">#ffffffff</color>
    <color name="system_neutral1_10">#fffbfbfb</color>
    <color name="system_neutral1_50">#fff0f0f0</color>
    <color name="system_neutral1_100">#ffe2e2e2</color>
    <color name="system_neutral1_200">#ffc6c6c6</color>
    <color name="system_neutral1_300">#ffababab</color>
    <color name="system_neutral1_400">#ff909090</color>
    <color name="system_neutral1_500">#ff757575</color>
    <color name="system_neutral1_600">#ff5e5e5e</color>
    <color name="system_neutral1_700">#ff464646</color>
    <color name="system_neutral1_800">#ff303030</color>
    <color name="system_neutral1_900">#ff1b1b1b</color>
    <color name="system_neutral1_1000">#ff000000</color>

    <color name="system_neutral2_0">#ffffffff</color>
    <color name="system_neutral2_10">#fffbfbfb</color>
    <color name="system_neutral2_50">#fff0f0f0</color>
    <color name="system_neutral2_100">#ffe2e2e2</color>
    <color name="system_neutral2_200">#ffc6c6c6</color>
    <color name="system_neutral2_300">#ffababab</color>
    <color name="system_neutral2_400">#ff909090</color>
    <color name="system_neutral2_500">#ff757575</color>
    <color name="system_neutral2_600">#ff5e5e5e</color>
    <color name="system_neutral2_700">#ff464646</color>
    <color name="system_neutral2_800">#ff303030</color>
    <color name="system_neutral2_900">#ff1b1b1b</color>
    <color name="system_neutral2_1000">#ff000000</color>

    <color name="system_accent1_0">#ffffffff</color>
    <color name="system_accent1_10">#fffdfcff</color>
    <color name="system_accent1_50">#ffecf3fe</color>
    <color name="system_accent1_100">#ffd3e3fd</color>
    <color name="system_accent1_200">#ffa8c7fa</color>
    <color name="system_accent1_300">#ff7cacf8</color>
    <color name="system_accent1_400">#ff4c8df6</color>
    <color name="system_accent1_500">#ff1b6ef3</color>
    <color name="system_accent1_600">#ff0b57d0</color>
    <color name="system_accent1_700">#ff0842a0</color>
    <color name="system_accent1_800">#ff062e6f</color>
    <color name="system_accent1_900">#ff041e49</color>
    <color name="system_accent1_1000">#ff000000</color>

    <color name="system_accent2_0">#ffffffff</color>
    <color name="system_accent2_10">#fffdfbff</color>
    <color name="system_accent2_50">#ffedeeff</color>
    <color name="system_accent2_100">#ffdfe0ff</color>
    <color name="system_accent2_200">#ffc3c4e2</color>
    <color name="system_accent2_300">#ffa8a9c6</color>
    <color name="system_accent2_400">#ff8d8fab</color>
    <color name="system_accent2_500">#ff72738f</color>
    <color name="system_accent2_600">#ff5a5c77</color>
    <color name="system_accent2_700">#ff43455e</color>
    <color name="system_accent2_800">#ff2c2f46</color>
    <color name="system_accent2_900">#ff161a30</color>
    <color name="system_accent2_1000">#ff000000</color>

    <color name="system_accent3_0">#ffffffff</color>
    <color name="system_accent3_10">#fffffbfd</color>
    <color name="system_accent3_50">#ffffe7ff</color>
    <color name="system_accent3_100">#fff5d9ff</color>
    <color name="system_accent3_200">#ffd8bde6</color>
    <color name="system_accent3_300">#ffbca2ca</color>
    <color name="system_accent3_400">#ffa187ae</color>
    <color name="system_accent3_500">#ff846c91</color>
    <color name="system_accent3_600">#ff6d567a</color>
    <color name="system_accent3_700">#ff553e61</color>
    <color name="system_accent3_800">#ff3d2849</color>
    <color name="system_accent3_900">#ff271332</color>
    <color name="system_accent3_1000">#ff000000</color>

    <color name="background_device_default_dark">@color/system_neutral1_900</color>
    <color name="background_device_default_light">@color/system_neutral1_50</color>
    <color name="background_floating_device_default_dark">@color/system_neutral2_900</color>
    <color name="background_floating_device_default_light">@color/system_neutral2_50</color>

    <color name="surface_dark">@color/system_neutral1_800</color>
    <color name="surface_header_dark">@color/system_neutral1_700</color>
    <color name="surface_header_light">@color/system_neutral1_100</color>
    <color name="surface_highlight_light">@color/system_neutral1_0</color>
    <color name="surface_variant_dark">@color/system_neutral1_700</color>
    <color name="surface_variant_light">@color/system_neutral2_100</color>

    <color name="accent_primary_device_default">@color/system_accent1_100</color>

    <!-- Primary colors -->
    <color name="md_primary">#0D68F1</color>
    <color name="md_on_primary">#FFFFFF</color>
    <color name="md_primary_container">#004B71</color>
    <color name="md_on_primary_container">#CBE6FF</color>
    <color name="md_inverse_primary">#03A9F4</color>

    <!-- Secondary/Accent colors -->
    <color name="md_secondary">#FCFCFD</color>
    <color name="md_on_secondary">#5E1133</color>
    <color name="md_secondary_container">#7D2949</color>
    <color name="md_on_secondary_container">#FFD9E2</color>

    <!-- Tertiary colors -->
    <color name="md_tertiary">#E9B3F0</color>
    <color name="md_on_tertiary">#4A1452</color>
    <color name="md_tertiary_container">#662B6B</color>
    <color name="md_on_tertiary_container">#FFD6FE</color>

    <!-- Background colors -->
    <color name="md_background">#FFFFFF</color>
    <color name="md_on_background">#1D1B20</color>

    <!-- Surface colors -->
    <color name="md_surface">#FFFFFF</color>
    <color name="md_on_surface">#1D1B20</color>
    <color name="md_surface_variant">#41484D</color>
    <color name="md_on_surface_variant">#C2C7CC</color>
    <color name="md_inverse_surface">#1D1B20</color>
    <color name="md_inverse_on_surface">#FFFFFF</color>
    <color name="md_surface_bright">#363F44</color>
    <color name="md_surface_dim">#121B1E</color>
    <color name="md_surface_container">#1A2428</color>
    <color name="md_surface_container_high">#272F33</color>
    <color name="md_surface_container_highest">#343B3F</color>
    <color name="md_surface_container_low">#1A2428</color>
    <color name="md_surface_container_lowest">#0B1518</color>

    <!-- Error colors -->
    <color name="md_error">#FFB4AB</color>
    <color name="md_on_error">#690005</color>
    <color name="md_error_container">#93000A</color>
    <color name="md_on_error_container">#FFDAD6</color>

    <!-- Outline colors -->
    <color name="md_outline">#F3F3F6</color>
    <color name="md_outline_variant">#41484D</color>

    <!-- Scrim -->
    <color name="md_scrim">#000000</color>

    <!-- Custom semantic colors for specific use cases -->
    <color name="md_surface_container_text">@color/md_surface_container_highest</color>
    <color name="md_surface_container_card">@color/md_surface_container</color>
    <color name="md_surface_container_dialog">@color/md_surface_container_high</color>
    <color name="md_surface_container_navigation">@color/md_surface_container_low</color>

    <!-- Material emphasis levels for text and icons -->
    <color name="md_text_primary">@color/md_on_surface</color>
    <color name="md_text_secondary">@color/md_on_surface_variant</color>
    <color name="md_text_disabled">@color/md_on_surface_variant</color>

    <!-- Common UI element colors -->
    <color name="md_divider">@color/md_outline_variant</color>
    <color name="md_ripple">@color/md_primary</color>
    <color name="md_elevated_surface">@color/md_surface_container</color>
    <color name="md_input_background">@color/md_surface_container_highest</color>

    <!-- Legacy colors - keep if needed -->
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="bg_dialog_loading_ads">#FFFFFF</color>

    <!-- Start page -->
    <color name="start_page_background">#000000</color>
    <color name="start_page_on_background">#E6E0E9</color>
    <color name="white_alpha_50">#80FFFFFF</color>
    <color name="description_color">#FF8C8C8C</color>
    <color name="primary_alpha_20">#300D68F1</color>
    <color name="primary_alpha_10">#260D68F1</color>
    <color name="start_page_bg">#FFFFFF</color>
    <color name="start_page_native_bg">#252628</color>
    <color name="start_page_main_text">#FFFFFF</color>
    <color name="start_page_sub_text">#B8BCCC</color>
    <color name="md_on_background_alpha_30">#4D1D1B20</color>
</resources>
